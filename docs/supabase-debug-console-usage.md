# Supabase Realtime Debug Console - Usage Guide

## Overview

The Supabase Realtime Debug Console is a comprehensive developer tool designed to diagnose and resolve connection loss issues during browser refresh and tab switching scenarios in the PlayBeg application.

## Quick Start

### 1. Automatic Initialization

The debug console is automatically initialized in development mode. No additional setup required!

```typescript
// Already integrated in src/main.tsx
if (process.env.NODE_ENV === 'development') {
  debugSupabase.init({
    supabaseClient: supabase,
    enableDashboard: true,
    enableTesting: true,
    enableLogging: true,
    logLevel: 'debug'
  });
}
```

### 2. Access the Dashboard

Once the application loads in development mode, you'll see the debug dashboard in the bottom-right corner of the screen.

## Features

### 🔌 Connection Monitoring
- **Real-time connection status** with visual indicators
- **WebSocket event tracking** (open, close, error, message)
- **Heartbeat monitoring** with ping/pong timing
- **Authentication state tracking** with token refresh detection
- **Channel subscription management** with state monitoring

### 📊 Event Timeline
- **Chronological event log** with timestamps and context
- **Event filtering** by type (connection, auth, visibility, heartbeat, channel, error)
- **Detailed event data** with expandable JSON views
- **Root cause analysis** with pattern detection

### ⚡ Performance Metrics
- **State update latency** tracking (<200ms requirement)
- **Memory usage** monitoring
- **Event processing time** analysis
- **Reconnection performance** statistics
- **Performance grading** (A-F scale)

### 🧪 Scenario Testing
- **Automated test scenarios** for common failure modes
- **Manual simulation controls** for testing edge cases
- **Test result tracking** with success/failure metrics
- **Custom scenario registration** for specific use cases

### 📈 Data Export
- **JSON log export** for detailed analysis
- **Performance report generation**
- **Timeline data extraction**
- **Console logging** for debugging

## Dashboard Interface

### Connection Panel
- Current connection status and metadata
- Active channel information with states
- Authentication status and token info
- Last error details and reconnection attempts

### Timeline Panel
- Chronological event history
- Event type filtering and search
- Detailed event data inspection
- Context information for each event

### Performance Panel
- Real-time performance metrics
- Latency charts and statistics
- Memory usage tracking
- Performance grade calculation

### Control Panel
- Manual connection controls (force reconnect)
- Simulation tools (tab switching, network changes)
- Scenario testing interface
- Data export functions

## Testing Scenarios

### Available Test Scenarios

#### 1. Browser Refresh Test
```typescript
await debugSupabase.runScenario('browser_refresh');
```
- Simulates browser refresh behavior
- Tests connection recovery
- Validates subscription restoration

#### 2. Tab Switching Test
```typescript
await debugSupabase.runScenario('tab_switching');
```
- Simulates tab hidden/visible states
- Tests background connection handling
- Validates reconnection on tab focus

#### 3. Network Transition Test
```typescript
await debugSupabase.runScenario('network_transition');
```
- Simulates offline/online transitions
- Tests network failure recovery
- Validates automatic reconnection

#### 4. Token Expiration Test
```typescript
await debugSupabase.runScenario('token_expiration');
```
- Simulates auth token expiry
- Tests token refresh handling
- Validates connection maintenance

#### 5. Multiple Channels Test
```typescript
await debugSupabase.runScenario('multiple_channels');
```
- Tests concurrent channel subscriptions
- Validates channel recovery after disconnection
- Tests subscription state management

### Manual Testing Controls

#### Connection Controls
- **Force Reconnect**: Manually trigger reconnection with exponential backoff
- **Simulate Tab Hidden/Visible**: Test visibility change handling
- **Simulate Offline/Online**: Test network state transitions

#### Simulation Functions
```typescript
// Access debug instance globally in development
const debug = window.debugSupabase;

// Force reconnection
await debug.forceReconnect();

// Run specific scenario
await debug.runScenario('browser_refresh');

// Export logs
const logs = debug.exportLogs();
console.log(logs);
```

## Performance Requirements

### State Update Latency
- **Target**: <200ms for all state updates
- **Monitoring**: Real-time latency tracking
- **Alerts**: Visual indicators when exceeding thresholds

### Memory Usage
- **Target**: <50MB for debug console overhead
- **Monitoring**: Continuous memory tracking
- **Optimization**: Automatic cleanup and garbage collection

### Event Processing
- **Target**: <10ms per event processing
- **Monitoring**: Processing time measurement
- **Optimization**: Batched updates and efficient algorithms

## Troubleshooting

### Common Issues

#### Dashboard Not Appearing
- Ensure you're in development mode (`NODE_ENV=development`)
- Check browser console for initialization errors
- Verify Supabase client is properly configured

#### Connection Not Detected
- Check if Supabase client is creating realtime connections
- Verify WebSocket instrumentation is working
- Look for connection events in the timeline

#### Performance Issues
- Check state update latency in Performance panel
- Monitor memory usage for leaks
- Review event processing times

#### Test Scenarios Failing
- Check browser console for detailed error messages
- Review test scenario configuration
- Verify mock implementations for testing environment

### Debug Console API

#### Global Access (Development Only)
```typescript
// Available in browser console during development
window.debugSupabase.getConnectionState()
window.debugSupabase.getEventTimeline()
window.debugSupabase.exportLogs()
window.debugSupabase.forceReconnect()
```

#### Programmatic Access
```typescript
import { debugSupabase } from './debug/debugSupabase';

const instance = debugSupabase.getInstance();
if (instance) {
  const state = instance.getConnectionState();
  const timeline = instance.getEventTimeline();
}
```

## Production Safety

### Tree Shaking
The debug console is completely removed from production builds:

```typescript
// Automatically tree-shaken in production
const debugSupabase = process.env.NODE_ENV === 'development' 
  ? await import('./debugSupabase')
  : { init: () => {}, /* no-op implementations */ };
```

### Performance Impact
- **Development**: <5MB memory overhead, <2% CPU usage
- **Production**: Zero runtime overhead (completely removed)
- **Bundle Size**: <50KB gzipped in development builds

## Advanced Usage

### Custom Scenario Registration
```typescript
debugSupabase.getInstance()?.testing.registerScenario({
  name: 'custom_test',
  description: 'Custom test scenario',
  timeout: 10000,
  expectedOutcome: 'Custom behavior validation',
  steps: [
    {
      name: 'setup',
      action: async () => {
        // Custom test logic
      },
      validation: async () => {
        // Custom validation
        return true;
      }
    }
  ]
});
```

### Event Filtering and Analysis
```typescript
const instance = debugSupabase.getInstance();
if (instance) {
  // Filter events by type
  const connectionEvents = instance.logger.filterLogs({
    level: 'info',
    message: 'connection'
  });
  
  // Get performance statistics
  const stats = instance.logger.getLogStatistics();
  console.log('Performance stats:', stats);
}
```

### Real-time Monitoring Integration
```typescript
// Subscribe to state changes
const instance = debugSupabase.getInstance();
if (instance) {
  const unsubscribe = instance.store.subscribe((state) => {
    if (!state.connection.isConnected) {
      // Handle disconnection
      console.warn('Connection lost:', state.connection.lastError);
    }
  });
  
  // Cleanup
  return unsubscribe;
}
```

## Best Practices

1. **Use in Development Only**: Never enable in production
2. **Monitor Performance**: Keep an eye on the performance metrics
3. **Test Scenarios Regularly**: Run scenarios during development
4. **Export Logs**: Save logs when investigating issues
5. **Custom Scenarios**: Create scenarios for your specific use cases
6. **Timeline Analysis**: Use the timeline to understand event sequences

## Support

For issues or questions about the Supabase Debug Console:
1. Check the browser console for error messages
2. Review the event timeline for connection patterns
3. Export logs for detailed analysis
4. Test with different scenarios to isolate issues

The debug console provides comprehensive visibility into Supabase realtime connection behavior, making it easier to diagnose and resolve connection issues in the PlayBeg application.
