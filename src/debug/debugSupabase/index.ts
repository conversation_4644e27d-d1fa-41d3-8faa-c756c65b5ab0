/**
 * Supabase Realtime Debug Console
 * Developer-only tool for diagnosing connection issues
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { ConnectionMonitor } from './core/ConnectionMonitor';
import { EventLogger } from './core/EventLogger';
import { RecoveryManager } from './core/RecoveryManager';
import { DebugStateStore } from './core/DebugStateStore';
import { ScenarioRunner } from './testing/ScenarioRunner';
import { DebugDashboard } from './ui/DebugDashboard';

export interface DebugSupabaseConfig {
  supabaseClient: SupabaseClient;
  enableDashboard?: boolean;
  enableTesting?: boolean;
  enableLogging?: boolean;
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
  maxLogEntries?: number;
  heartbeatInterval?: number;
}

export interface DebugSupabaseInstance {
  monitor: ConnectionMonitor;
  logger: EventLogger;
  recovery: RecoveryManager;
  store: DebugStateStore;
  testing: ScenarioRunner;
  dashboard: DebugDashboard | null;
  
  // Public API
  getConnectionState(): ConnectionState;
  getEventTimeline(): EventTimelineEntry[];
  exportLogs(): string;
  forceReconnect(): Promise<void>;
  runScenario(scenario: string): Promise<void>;
  destroy(): void;
}

export interface ConnectionState {
  isConnected: boolean;
  connectionId: string | null;
  lastHeartbeat: number | null;
  authState: 'authenticated' | 'unauthenticated' | 'refreshing';
  activeChannels: ChannelInfo[];
  reconnectAttempts: number;
  lastError: Error | null;
}

export interface ChannelInfo {
  id: string;
  topic: string;
  state: 'joining' | 'joined' | 'leaving' | 'closed' | 'errored';
  subscriptionCount: number;
  lastEvent: number | null;
  errorCount: number;
}

export interface EventTimelineEntry {
  id: string;
  timestamp: number;
  type: 'connection' | 'auth' | 'visibility' | 'heartbeat' | 'channel' | 'error';
  event: string;
  data: any;
  context: {
    visibilityState: DocumentVisibilityState;
    connectionState: 'connected' | 'disconnected' | 'reconnecting';
    authState: 'authenticated' | 'unauthenticated' | 'refreshing';
    activeChannels: number;
  };
}

class DebugSupabaseCore implements DebugSupabaseInstance {
  public monitor: ConnectionMonitor;
  public logger: EventLogger;
  public recovery: RecoveryManager;
  public store: DebugStateStore;
  public testing: ScenarioRunner;
  public dashboard: DebugDashboard | null = null;

  private config: DebugSupabaseConfig;
  private isInitialized = false;

  constructor(config: DebugSupabaseConfig) {
    this.config = config;
    
    // Initialize core modules
    this.store = new DebugStateStore();
    this.logger = new EventLogger(this.store, {
      maxEntries: config.maxLogEntries || 1000,
      logLevel: config.logLevel || 'debug'
    });
    
    this.monitor = new ConnectionMonitor(
      config.supabaseClient,
      this.store,
      this.logger,
      {
        heartbeatInterval: config.heartbeatInterval || 30000
      }
    );
    
    this.recovery = new RecoveryManager(
      config.supabaseClient,
      this.store,
      this.logger
    );
    
    this.testing = new ScenarioRunner(
      config.supabaseClient,
      this.store,
      this.logger
    );

    // Initialize dashboard if enabled
    if (config.enableDashboard) {
      this.dashboard = new DebugDashboard(this.store, this.logger, this);
    }
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Start monitoring
      await this.monitor.start();
      
      // Initialize recovery manager
      await this.recovery.initialize();
      
      // Setup testing if enabled
      if (this.config.enableTesting) {
        await this.testing.initialize();
      }
      
      // Mount dashboard if enabled
      if (this.dashboard) {
        this.dashboard.mount();
      }
      
      this.logger.info('DebugSupabase initialized successfully');
      this.isInitialized = true;
      
    } catch (error) {
      this.logger.error('Failed to initialize DebugSupabase', error);
      throw error;
    }
  }

  getConnectionState(): ConnectionState {
    return this.store.getConnectionState();
  }

  getEventTimeline(): EventTimelineEntry[] {
    return this.logger.getTimeline();
  }

  exportLogs(): string {
    const data = {
      timestamp: new Date().toISOString(),
      connectionState: this.getConnectionState(),
      timeline: this.getEventTimeline(),
      config: {
        heartbeatInterval: this.config.heartbeatInterval,
        logLevel: this.config.logLevel,
        maxLogEntries: this.config.maxLogEntries
      }
    };
    
    return JSON.stringify(data, null, 2);
  }

  async forceReconnect(): Promise<void> {
    this.logger.info('Force reconnect triggered');
    await this.recovery.forceReconnect();
  }

  async runScenario(scenario: string): Promise<void> {
    if (!this.config.enableTesting) {
      throw new Error('Testing is not enabled');
    }
    
    this.logger.info(`Running scenario: ${scenario}`);
    await this.testing.runScenario(scenario);
  }

  destroy(): void {
    this.logger.info('Destroying DebugSupabase instance');
    
    this.monitor.stop();
    this.recovery.destroy();
    this.testing.destroy();
    
    if (this.dashboard) {
      this.dashboard.unmount();
    }
    
    this.store.clear();
    this.isInitialized = false;
  }
}

// Singleton instance
let debugInstance: DebugSupabaseCore | null = null;

export const debugSupabase = {
  /**
   * Initialize the debug console
   */
  async init(config: DebugSupabaseConfig): Promise<DebugSupabaseInstance> {
    if (process.env.NODE_ENV !== 'development') {
      // Return no-op implementation for production
      return createNoOpInstance();
    }

    if (debugInstance) {
      console.warn('DebugSupabase already initialized');
      return debugInstance;
    }

    debugInstance = new DebugSupabaseCore(config);
    await debugInstance.initialize();
    
    // Make available globally for console access
    if (typeof window !== 'undefined') {
      (window as any).debugSupabase = debugInstance;
    }
    
    return debugInstance;
  },

  /**
   * Get current instance
   */
  getInstance(): DebugSupabaseInstance | null {
    return debugInstance;
  },

  /**
   * Destroy current instance
   */
  destroy(): void {
    if (debugInstance) {
      debugInstance.destroy();
      debugInstance = null;
    }
  }
};

// No-op implementation for production
function createNoOpInstance(): DebugSupabaseInstance {
  const noop = () => {};
  const noopAsync = async () => {};
  
  return {
    monitor: {} as any,
    logger: {} as any,
    recovery: {} as any,
    store: {} as any,
    testing: {} as any,
    dashboard: null,
    
    getConnectionState: () => ({
      isConnected: false,
      connectionId: null,
      lastHeartbeat: null,
      authState: 'unauthenticated' as const,
      activeChannels: [],
      reconnectAttempts: 0,
      lastError: null
    }),
    getEventTimeline: () => [],
    exportLogs: () => '{}',
    forceReconnect: noopAsync,
    runScenario: noopAsync,
    destroy: noop
  };
}

// Export types
export type {
  DebugSupabaseConfig,
  DebugSupabaseInstance,
  ConnectionState,
  ChannelInfo,
  EventTimelineEntry
};

// Export core modules for advanced usage
export {
  ConnectionMonitor,
  EventLogger,
  RecoveryManager,
  DebugStateStore,
  ScenarioRunner
};
