/**
 * Centralized state management for debug console
 * Provides reactive state updates with <200ms performance requirement
 */

import { ConnectionState, ChannelInfo, EventTimelineEntry } from '../index';

export interface DebugState {
  connection: ConnectionState;
  channels: Map<string, ChannelInfo>;
  timeline: EventTimelineEntry[];
  performance: PerformanceMetrics;
  testing: TestingState;
}

export interface PerformanceMetrics {
  stateUpdateLatency: number[];
  memoryUsage: number;
  eventProcessingTime: number[];
  reconnectionTimes: number[];
}

export interface TestingState {
  activeScenario: string | null;
  scenarioResults: Map<string, ScenarioResult>;
  isRunning: boolean;
}

export interface ScenarioResult {
  scenario: string;
  startTime: number;
  endTime: number | null;
  success: boolean;
  error: Error | null;
  metrics: any;
}

type StateListener = (state: DebugState) => void;
type StateSelector<T> = (state: DebugState) => T;

export class DebugStateStore {
  private state: DebugState;
  private listeners: Set<StateListener> = new Set();
  private selectorCache: Map<string, any> = new Map();
  private updateQueue: Array<() => void> = [];
  private isUpdating = false;

  constructor() {
    this.state = {
      connection: {
        isConnected: false,
        connectionId: null,
        lastHeartbeat: null,
        authState: 'unauthenticated',
        activeChannels: [],
        reconnectAttempts: 0,
        lastError: null
      },
      channels: new Map(),
      timeline: [],
      performance: {
        stateUpdateLatency: [],
        memoryUsage: 0,
        eventProcessingTime: [],
        reconnectionTimes: []
      },
      testing: {
        activeScenario: null,
        scenarioResults: new Map(),
        isRunning: false
      }
    };

    // Performance monitoring
    this.startPerformanceMonitoring();
  }

  /**
   * Subscribe to state changes
   */
  subscribe(listener: StateListener): () => void {
    this.listeners.add(listener);
    
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * Get current state (immutable)
   */
  getState(): Readonly<DebugState> {
    return this.state;
  }

  /**
   * Select specific part of state with memoization
   */
  select<T>(selector: StateSelector<T>, cacheKey?: string): T {
    if (cacheKey && this.selectorCache.has(cacheKey)) {
      return this.selectorCache.get(cacheKey);
    }

    const result = selector(this.state);
    
    if (cacheKey) {
      this.selectorCache.set(cacheKey, result);
    }
    
    return result;
  }

  /**
   * Update state with batching for performance
   */
  updateState(updater: (state: DebugState) => Partial<DebugState>): void {
    const startTime = performance.now();
    
    this.updateQueue.push(() => {
      const updates = updater(this.state);
      this.state = { ...this.state, ...updates };
      
      // Clear selector cache
      this.selectorCache.clear();
      
      // Track performance
      const latency = performance.now() - startTime;
      this.state.performance.stateUpdateLatency.push(latency);
      
      // Keep only last 100 measurements
      if (this.state.performance.stateUpdateLatency.length > 100) {
        this.state.performance.stateUpdateLatency.shift();
      }
    });

    this.processUpdateQueue();
  }

  /**
   * Process update queue with batching
   */
  private async processUpdateQueue(): Promise<void> {
    if (this.isUpdating || this.updateQueue.length === 0) {
      return;
    }

    this.isUpdating = true;

    // Process all queued updates in a single batch
    while (this.updateQueue.length > 0) {
      const update = this.updateQueue.shift()!;
      update();
    }

    // Notify listeners asynchronously to maintain <200ms requirement
    await this.notifyListeners();
    
    this.isUpdating = false;
  }

  /**
   * Notify all listeners with performance tracking
   */
  private async notifyListeners(): Promise<void> {
    const startTime = performance.now();
    
    // Use requestAnimationFrame for optimal performance
    return new Promise(resolve => {
      requestAnimationFrame(() => {
        this.listeners.forEach(listener => {
          try {
            listener(this.state);
          } catch (error) {
            console.error('Error in state listener:', error);
          }
        });
        
        const notificationTime = performance.now() - startTime;
        
        // Ensure we meet the <200ms requirement
        if (notificationTime > 200) {
          console.warn(`State update took ${notificationTime}ms (>200ms requirement)`);
        }
        
        resolve();
      });
    });
  }

  /**
   * Connection state helpers
   */
  getConnectionState(): ConnectionState {
    return this.state.connection;
  }

  updateConnectionState(updates: Partial<ConnectionState>): void {
    this.updateState(state => ({
      connection: { ...state.connection, ...updates }
    }));
  }

  /**
   * Channel management
   */
  addChannel(channelInfo: ChannelInfo): void {
    this.updateState(state => {
      const newChannels = new Map(state.channels);
      newChannels.set(channelInfo.id, channelInfo);
      
      return {
        channels: newChannels,
        connection: {
          ...state.connection,
          activeChannels: Array.from(newChannels.values())
        }
      };
    });
  }

  updateChannel(channelId: string, updates: Partial<ChannelInfo>): void {
    this.updateState(state => {
      const newChannels = new Map(state.channels);
      const existing = newChannels.get(channelId);
      
      if (existing) {
        newChannels.set(channelId, { ...existing, ...updates });
      }
      
      return {
        channels: newChannels,
        connection: {
          ...state.connection,
          activeChannels: Array.from(newChannels.values())
        }
      };
    });
  }

  removeChannel(channelId: string): void {
    this.updateState(state => {
      const newChannels = new Map(state.channels);
      newChannels.delete(channelId);
      
      return {
        channels: newChannels,
        connection: {
          ...state.connection,
          activeChannels: Array.from(newChannels.values())
        }
      };
    });
  }

  /**
   * Timeline management
   */
  addTimelineEntry(entry: EventTimelineEntry): void {
    this.updateState(state => {
      const newTimeline = [...state.timeline, entry];
      
      // Keep only last 500 entries for performance
      if (newTimeline.length > 500) {
        newTimeline.shift();
      }
      
      return { timeline: newTimeline };
    });
  }

  /**
   * Testing state management
   */
  setActiveScenario(scenario: string | null): void {
    this.updateState(state => ({
      testing: {
        ...state.testing,
        activeScenario: scenario,
        isRunning: scenario !== null
      }
    }));
  }

  addScenarioResult(result: ScenarioResult): void {
    this.updateState(state => {
      const newResults = new Map(state.testing.scenarioResults);
      newResults.set(result.scenario, result);
      
      return {
        testing: {
          ...state.testing,
          scenarioResults: newResults
        }
      };
    });
  }

  /**
   * Performance monitoring
   */
  private startPerformanceMonitoring(): void {
    // Monitor memory usage every 10 seconds
    setInterval(() => {
      if ('memory' in performance) {
        const memInfo = (performance as any).memory;
        this.updateState(state => ({
          performance: {
            ...state.performance,
            memoryUsage: memInfo.usedJSHeapSize
          }
        }));
      }
    }, 10000);
  }

  addPerformanceMetric(type: keyof PerformanceMetrics, value: number): void {
    this.updateState(state => {
      const metrics = { ...state.performance };
      
      if (Array.isArray(metrics[type])) {
        (metrics[type] as number[]).push(value);
        
        // Keep only last 100 measurements
        if ((metrics[type] as number[]).length > 100) {
          (metrics[type] as number[]).shift();
        }
      } else {
        (metrics as any)[type] = value;
      }
      
      return { performance: metrics };
    });
  }

  /**
   * Clear all state
   */
  clear(): void {
    this.listeners.clear();
    this.selectorCache.clear();
    this.updateQueue.length = 0;
    
    this.state = {
      connection: {
        isConnected: false,
        connectionId: null,
        lastHeartbeat: null,
        authState: 'unauthenticated',
        activeChannels: [],
        reconnectAttempts: 0,
        lastError: null
      },
      channels: new Map(),
      timeline: [],
      performance: {
        stateUpdateLatency: [],
        memoryUsage: 0,
        eventProcessingTime: [],
        reconnectionTimes: []
      },
      testing: {
        activeScenario: null,
        scenarioResults: new Map(),
        isRunning: false
      }
    };
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): {
    avgStateUpdateLatency: number;
    maxStateUpdateLatency: number;
    memoryUsage: string;
    totalEvents: number;
  } {
    const latencies = this.state.performance.stateUpdateLatency;
    
    return {
      avgStateUpdateLatency: latencies.length > 0 
        ? latencies.reduce((a, b) => a + b, 0) / latencies.length 
        : 0,
      maxStateUpdateLatency: latencies.length > 0 
        ? Math.max(...latencies) 
        : 0,
      memoryUsage: `${(this.state.performance.memoryUsage / 1024 / 1024).toFixed(2)}MB`,
      totalEvents: this.state.timeline.length
    };
  }
}
