/**
 * Connection Monitor - Tracks Supabase realtime connection health
 * Instruments WebSocket events, heartbeats, and visibility changes
 */

import { SupabaseClient, RealtimeChannel } from '@supabase/supabase-js';
import { DebugStateStore } from './DebugStateStore';
import { EventLogger } from './EventLogger';
import { ChannelInfo } from '../index';

export interface ConnectionMonitorConfig {
  heartbeatInterval: number;
  connectionTimeout: number;
  maxReconnectAttempts: number;
}

export class ConnectionMonitor {
  private supabase: SupabaseClient;
  private store: DebugStateStore;
  private logger: EventLogger;
  private config: ConnectionMonitorConfig;
  
  private isMonitoring = false;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private connectionCheckTimer: NodeJS.Timeout | null = null;
  private visibilityChangeHandler: (() => void) | null = null;
  private authStateHandler: (() => void) | null = null;
  
  // WebSocket instrumentation
  private originalWebSocket: typeof WebSocket | null = null;
  private instrumentedChannels = new Map<string, RealtimeChannel>();
  
  // Connection state tracking
  private lastHeartbeatSent = 0;
  private lastHeartbeatReceived = 0;
  private connectionStartTime = 0;

  constructor(
    supabase: SupabaseClient,
    store: DebugStateStore,
    logger: EventLogger,
    config: Partial<ConnectionMonitorConfig> = {}
  ) {
    this.supabase = supabase;
    this.store = store;
    this.logger = logger;
    this.config = {
      heartbeatInterval: 30000,
      connectionTimeout: 10000,
      maxReconnectAttempts: 5,
      ...config
    };
  }

  async start(): Promise<void> {
    if (this.isMonitoring) return;

    this.logger.info('Starting connection monitoring');
    this.isMonitoring = true;
    this.connectionStartTime = Date.now();

    // Instrument WebSocket
    this.instrumentWebSocket();

    // Instrument Supabase client
    this.instrumentSupabaseClient();

    // Setup visibility change monitoring
    this.setupVisibilityMonitoring();

    // Setup auth state monitoring
    this.setupAuthStateMonitoring();

    // Monitor Supabase realtime directly
    this.monitorSupabaseRealtime();

    // Start heartbeat monitoring
    this.startHeartbeatMonitoring();

    // Start connection health checks
    this.startConnectionHealthChecks();

    // Fix: Don't assume connected until we verify WebSocket
    this.store.updateConnectionState({
      isConnected: false, // Start as disconnected until WebSocket confirms
      connectionId: this.generateConnectionId()
    });
  }

  stop(): void {
    if (!this.isMonitoring) return;

    this.logger.info('Stopping connection monitoring');
    this.isMonitoring = false;

    // Clear timers
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
    
    if (this.connectionCheckTimer) {
      clearInterval(this.connectionCheckTimer);
      this.connectionCheckTimer = null;
    }

    // Remove event listeners
    if (this.visibilityChangeHandler) {
      document.removeEventListener('visibilitychange', this.visibilityChangeHandler);
      this.visibilityChangeHandler = null;
    }

    // Restore original WebSocket
    this.restoreWebSocket();
    
    this.store.updateConnectionState({
      isConnected: false,
      connectionId: null
    });
  }

  /**
   * Instrument WebSocket to capture connection events
   */
  private instrumentWebSocket(): void {
    if (typeof window === 'undefined' || this.originalWebSocket) return;

    this.originalWebSocket = window.WebSocket;
    const monitor = this;

    window.WebSocket = class extends WebSocket {
      constructor(url: string | URL, protocols?: string | string[]) {
        super(url, protocols);
        
        monitor.logger.debug('WebSocket created', { url: url.toString() });
        
        // Instrument WebSocket events
        this.addEventListener('open', (event) => {
          monitor.logger.info('WebSocket connected', { url: url.toString() });
          monitor.store.updateConnectionState({
            isConnected: true,
            lastError: null
          });

          // Reset heartbeat tracking when WebSocket opens
          monitor.lastHeartbeatReceived = Date.now();
          monitor.lastHeartbeatSent = Date.now();

          monitor.store.addTimelineEntry({
            id: monitor.generateEventId(),
            timestamp: Date.now(),
            type: 'connection',
            event: 'websocket_open',
            data: { url: url.toString() },
            context: monitor.getCurrentContext()
          });
        });

        this.addEventListener('close', (event) => {
          monitor.logger.warn('WebSocket disconnected', { 
            code: event.code, 
            reason: event.reason,
            url: url.toString()
          });
          
          monitor.store.updateConnectionState({
            isConnected: false,
            lastError: new Error(`WebSocket closed: ${event.reason}`)
          });
          
          monitor.store.addTimelineEntry({
            id: monitor.generateEventId(),
            timestamp: Date.now(),
            type: 'connection',
            event: 'websocket_close',
            data: { code: event.code, reason: event.reason, url: url.toString() },
            context: monitor.getCurrentContext()
          });
        });

        this.addEventListener('error', (event) => {
          monitor.logger.error('WebSocket error', event);
          
          monitor.store.updateConnectionState({
            isConnected: false,
            lastError: new Error('WebSocket error')
          });
          
          monitor.store.addTimelineEntry({
            id: monitor.generateEventId(),
            timestamp: Date.now(),
            type: 'error',
            event: 'websocket_error',
            data: { error: event },
            context: monitor.getCurrentContext()
          });
        });

        this.addEventListener('message', (event) => {
          try {
            const data = JSON.parse(event.data);
            
            // Track heartbeat responses
            if (data.event === 'phx_reply' && data.payload?.status === 'ok') {
              monitor.lastHeartbeatReceived = Date.now();
              monitor.logger.debug('Heartbeat received');
              
              monitor.store.addTimelineEntry({
                id: monitor.generateEventId(),
                timestamp: Date.now(),
                type: 'heartbeat',
                event: 'pong_received',
                data: { latency: monitor.lastHeartbeatReceived - monitor.lastHeartbeatSent },
                context: monitor.getCurrentContext()
              });
            }
            
            // Track other realtime events
            monitor.logger.debug('WebSocket message', data);
            
          } catch (error) {
            monitor.logger.debug('Non-JSON WebSocket message', event.data);
          }
        });
      }
    };
  }

  /**
   * Restore original WebSocket
   */
  private restoreWebSocket(): void {
    if (typeof window !== 'undefined' && this.originalWebSocket) {
      window.WebSocket = this.originalWebSocket;
      this.originalWebSocket = null;
    }
  }

  /**
   * Instrument Supabase client to track channel operations
   */
  private instrumentSupabaseClient(): void {
    const originalChannel = this.supabase.channel.bind(this.supabase);
    
    this.supabase.channel = (name: string, opts?: any) => {
      const channel = originalChannel(name, opts);
      const channelId = this.generateChannelId(name);
      
      this.logger.debug('Channel created', { name, channelId });
      
      // Track channel in store
      const channelInfo: ChannelInfo = {
        id: channelId,
        topic: name,
        state: 'joining',
        subscriptionCount: 0,
        lastEvent: null,
        errorCount: 0
      };
      
      this.store.addChannel(channelInfo);
      this.instrumentedChannels.set(channelId, channel);
      
      // Instrument channel methods
      const originalSubscribe = channel.subscribe.bind(channel);
      channel.subscribe = (callback?: any) => {
        this.logger.info('Channel subscribing', { name, channelId });
        
        this.store.updateChannel(channelId, {
          state: 'joining',
          subscriptionCount: channelInfo.subscriptionCount + 1
        });
        
        const result = originalSubscribe(callback);
        
        // Track subscription status
        if (typeof callback === 'function') {
          const wrappedCallback = (status: string, err?: any) => {
            this.logger.debug('Channel subscription status', { name, channelId, status, err });
            
            this.store.updateChannel(channelId, {
              state: status === 'SUBSCRIBED' ? 'joined' : 
                     status === 'CLOSED' ? 'closed' : 
                     status === 'CHANNEL_ERROR' ? 'errored' : 'joining'
            });
            
            this.store.addTimelineEntry({
              id: this.generateEventId(),
              timestamp: Date.now(),
              type: 'channel',
              event: 'subscription_status',
              data: { channelId, name, status, error: err },
              context: this.getCurrentContext()
            });
            
            callback(status, err);
          };
          
          return originalSubscribe(wrappedCallback);
        }
        
        return result;
      };
      
      return channel;
    };
  }

  /**
   * Setup visibility change monitoring
   */
  private setupVisibilityMonitoring(): void {
    this.visibilityChangeHandler = () => {
      const visibilityState = document.visibilityState;
      
      this.logger.info('Visibility changed', { visibilityState });
      
      this.store.addTimelineEntry({
        id: this.generateEventId(),
        timestamp: Date.now(),
        type: 'visibility',
        event: 'visibility_change',
        data: { visibilityState },
        context: this.getCurrentContext()
      });
      
      // Trigger connection check when becoming visible
      if (visibilityState === 'visible') {
        this.checkConnectionHealth();
      }
    };
    
    document.addEventListener('visibilitychange', this.visibilityChangeHandler);
  }

  /**
   * Setup auth state monitoring
   */
  private setupAuthStateMonitoring(): void {
    this.authStateHandler = this.supabase.auth.onAuthStateChange((event, session) => {
      this.logger.info('Auth state changed', { event, userId: session?.user?.id });
      
      const authState = session ? 'authenticated' : 'unauthenticated';
      
      this.store.updateConnectionState({ authState });
      
      this.store.addTimelineEntry({
        id: this.generateEventId(),
        timestamp: Date.now(),
        type: 'auth',
        event: 'auth_state_change',
        data: { event, userId: session?.user?.id, authState },
        context: this.getCurrentContext()
      });
    }).data.subscription.unsubscribe;
  }

  /**
   * Start heartbeat monitoring
   */
  private startHeartbeatMonitoring(): void {
    this.heartbeatTimer = setInterval(() => {
      this.sendHeartbeat();
    }, this.config.heartbeatInterval);
  }

  /**
   * Send heartbeat ping
   */
  private sendHeartbeat(): void {
    this.lastHeartbeatSent = Date.now();
    
    this.logger.debug('Sending heartbeat');
    
    this.store.addTimelineEntry({
      id: this.generateEventId(),
      timestamp: Date.now(),
      type: 'heartbeat',
      event: 'ping_sent',
      data: { timestamp: this.lastHeartbeatSent },
      context: this.getCurrentContext()
    });
    
    this.store.updateConnectionState({
      lastHeartbeat: this.lastHeartbeatSent
    });
  }

  /**
   * Start connection health checks
   */
  private startConnectionHealthChecks(): void {
    this.connectionCheckTimer = setInterval(() => {
      this.checkConnectionHealth();
    }, 10000); // Check every 10 seconds
  }

  /**
   * Check connection health
   */
  private checkConnectionHealth(): void {
    const now = Date.now();

    // Fix: Only check if we have received at least one heartbeat
    if (this.lastHeartbeatReceived === 0) {
      // No heartbeat received yet - this is normal during initial connection
      return;
    }

    const timeSinceLastHeartbeat = now - this.lastHeartbeatReceived;
    const isHealthy = timeSinceLastHeartbeat < this.config.heartbeatInterval * 2;

    if (!isHealthy && this.store.getConnectionState().isConnected) {
      this.logger.warn('Connection appears unhealthy', {
        timeSinceLastHeartbeat,
        threshold: this.config.heartbeatInterval * 2,
        lastHeartbeatReceived: this.lastHeartbeatReceived,
        lastHeartbeatSent: this.lastHeartbeatSent
      });

      this.store.updateConnectionState({
        isConnected: false,
        lastError: new Error('Heartbeat timeout')
      });
    }
  }

  /**
   * Get current context for timeline entries
   */
  private getCurrentContext() {
    const connectionState = this.store.getConnectionState();
    
    return {
      visibilityState: document.visibilityState,
      connectionState: connectionState.isConnected ? 'connected' as const : 'disconnected' as const,
      authState: connectionState.authState,
      activeChannels: connectionState.activeChannels.length
    };
  }

  /**
   * Monitor Supabase Realtime directly
   */
  private monitorSupabaseRealtime(): void {
    let socketCreationAttempted = false;
    let realtimeCheckCount = 0;

    const checkRealtimeConnection = () => {
      try {
        realtimeCheckCount++;
        const realtime = (this.supabase as any).realtime;

        if (realtime) {
          // If no socket exists and we haven't tried yet, attempt once
          if (!realtime.socket && !socketCreationAttempted && realtimeCheckCount <= 3) {
            this.logger.warn('No realtime socket found - Supabase Realtime may be disabled');

            socketCreationAttempted = true;

            this.store.addTimelineEntry({
              id: this.generateEventId(),
              timestamp: Date.now(),
              type: 'connection',
              event: 'realtime_not_available',
              data: {
                reason: 'no_socket_found',
                suggestion: 'Check if Realtime is enabled in Supabase dashboard'
              },
              context: this.getCurrentContext()
            });

            // Update connection state to reflect Realtime unavailability
            this.store.updateConnectionState({
              isConnected: false,
              lastError: new Error('Supabase Realtime appears to be disabled')
            });
          }

          // Check if socket now exists
          if (realtime.socket) {
            const socket = realtime.socket;
            const isConnected = socket.readyState === WebSocket.OPEN;

            this.logger.debug('Supabase realtime status', {
              readyState: socket.readyState,
              isConnected,
              url: socket.url
            });

            // Update connection state based on actual socket
            const currentState = this.store.getConnectionState();
            if (currentState.isConnected !== isConnected) {
              this.store.updateConnectionState({
                isConnected,
                lastError: isConnected ? null : new Error('Realtime socket disconnected')
              });

              this.store.addTimelineEntry({
                id: this.generateEventId(),
                timestamp: Date.now(),
                type: 'connection',
                event: isConnected ? 'realtime_connected' : 'realtime_disconnected',
                data: { readyState: socket.readyState, url: socket.url },
                context: this.getCurrentContext()
              });

              if (isConnected) {
                // Reset heartbeat when connected
                this.lastHeartbeatReceived = Date.now();
                this.lastHeartbeatSent = Date.now();

                this.store.updateConnectionState({
                  lastHeartbeat: Date.now()
                });
              }
            }
          } else {
            this.logger.debug('Still no realtime socket found');
          }
        }
      } catch (error) {
        this.logger.debug('Could not access Supabase realtime socket', error);
      }
    };

    // Check every 2 seconds
    setInterval(checkRealtimeConnection, 2000);

    // Initial check after a delay to let auth settle
    setTimeout(checkRealtimeConnection, 3000);
  }

  /**
   * Generate unique IDs
   */
  private generateConnectionId(): string {
    return `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateChannelId(name: string): string {
    return `ch_${name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
