/**
 * Event Logger - Comprehensive logging system for debug console
 * Captures all connection events with timestamps and context
 */

import { DebugStateStore } from './DebugStateStore';
import { EventTimelineEntry } from '../index';

export interface EventLoggerConfig {
  maxEntries: number;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  enableConsoleOutput: boolean;
  enablePerformanceTracking: boolean;
}

export interface LogEntry {
  id: string;
  timestamp: number;
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  data?: any;
  context?: any;
  performance?: {
    processingTime: number;
    memoryUsage: number;
  };
}

export class EventLogger {
  private store: DebugStateStore;
  private config: EventLoggerConfig;
  private logEntries: LogEntry[] = [];
  private logLevels = { debug: 0, info: 1, warn: 2, error: 3 };

  constructor(store: DebugStateStore, config: Partial<EventLoggerConfig> = {}) {
    this.store = store;
    this.config = {
      maxEntries: 1000,
      logLevel: 'debug',
      enableConsoleOutput: true,
      enablePerformanceTracking: true,
      ...config
    };
  }

  /**
   * Debug level logging
   */
  debug(message: string, data?: any, context?: any): void {
    this.log('debug', message, data, context);
  }

  /**
   * Info level logging
   */
  info(message: string, data?: any, context?: any): void {
    this.log('info', message, data, context);
  }

  /**
   * Warning level logging
   */
  warn(message: string, data?: any, context?: any): void {
    this.log('warn', message, data, context);
  }

  /**
   * Error level logging
   */
  error(message: string, error?: any, context?: any): void {
    this.log('error', message, error, context);
  }

  /**
   * Core logging method
   */
  private log(level: 'debug' | 'info' | 'warn' | 'error', message: string, data?: any, context?: any): void {
    const startTime = performance.now();
    
    // Check if we should log this level
    if (this.logLevels[level] < this.logLevels[this.config.logLevel]) {
      return;
    }

    const logEntry: LogEntry = {
      id: this.generateLogId(),
      timestamp: Date.now(),
      level,
      message,
      data,
      context
    };

    // Add performance tracking if enabled
    if (this.config.enablePerformanceTracking) {
      const processingTime = performance.now() - startTime;
      logEntry.performance = {
        processingTime,
        memoryUsage: this.getMemoryUsage()
      };
      
      // Track processing time in store
      this.store.addPerformanceMetric('eventProcessingTime', processingTime);
    }

    // Add to internal log
    this.logEntries.push(logEntry);
    
    // Maintain max entries limit
    if (this.logEntries.length > this.config.maxEntries) {
      this.logEntries.shift();
    }

    // Add to timeline if it's a significant event
    if (level !== 'debug' || this.isSignificantEvent(message)) {
      this.addToTimeline(logEntry);
    }

    // Console output if enabled
    if (this.config.enableConsoleOutput) {
      this.outputToConsole(logEntry);
    }
  }

  /**
   * Add log entry to timeline
   */
  private addToTimeline(logEntry: LogEntry): void {
    const timelineEntry: EventTimelineEntry = {
      id: logEntry.id,
      timestamp: logEntry.timestamp,
      type: this.inferEventType(logEntry.message),
      event: logEntry.message,
      data: logEntry.data,
      context: this.getCurrentContext()
    };

    this.store.addTimelineEntry(timelineEntry);
  }

  /**
   * Infer event type from log message
   */
  private inferEventType(message: string): EventTimelineEntry['type'] {
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('websocket') || lowerMessage.includes('connection')) {
      return 'connection';
    }
    if (lowerMessage.includes('auth') || lowerMessage.includes('login') || lowerMessage.includes('token')) {
      return 'auth';
    }
    if (lowerMessage.includes('visibility') || lowerMessage.includes('tab')) {
      return 'visibility';
    }
    if (lowerMessage.includes('heartbeat') || lowerMessage.includes('ping') || lowerMessage.includes('pong')) {
      return 'heartbeat';
    }
    if (lowerMessage.includes('channel') || lowerMessage.includes('subscription')) {
      return 'channel';
    }
    if (lowerMessage.includes('error') || lowerMessage.includes('failed')) {
      return 'error';
    }
    
    return 'connection'; // Default
  }

  /**
   * Check if event is significant enough for timeline
   */
  private isSignificantEvent(message: string): boolean {
    const significantKeywords = [
      'connected', 'disconnected', 'reconnecting', 'failed',
      'auth', 'token', 'visibility', 'heartbeat', 'channel',
      'subscription', 'error', 'timeout'
    ];
    
    const lowerMessage = message.toLowerCase();
    return significantKeywords.some(keyword => lowerMessage.includes(keyword));
  }

  /**
   * Output to browser console
   */
  private outputToConsole(logEntry: LogEntry): void {
    const prefix = `[DebugSupabase:${logEntry.level.toUpperCase()}]`;
    const timestamp = new Date(logEntry.timestamp).toISOString();
    const message = `${prefix} ${timestamp} ${logEntry.message}`;

    switch (logEntry.level) {
      case 'debug':
        console.debug(message, logEntry.data);
        break;
      case 'info':
        console.info(message, logEntry.data);
        break;
      case 'warn':
        console.warn(message, logEntry.data);
        break;
      case 'error':
        console.error(message, logEntry.data);
        break;
    }
  }

  /**
   * Get current context for timeline entries
   */
  private getCurrentContext() {
    const connectionState = this.store.getConnectionState();
    
    return {
      visibilityState: document.visibilityState,
      connectionState: connectionState.isConnected ? 'connected' as const : 'disconnected' as const,
      authState: connectionState.authState,
      activeChannels: connectionState.activeChannels.length
    };
  }

  /**
   * Get memory usage
   */
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }

  /**
   * Generate unique log ID
   */
  private generateLogId(): string {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get all log entries
   */
  getLogEntries(): LogEntry[] {
    return [...this.logEntries];
  }

  /**
   * Get timeline entries
   */
  getTimeline(): EventTimelineEntry[] {
    return this.store.select(state => state.timeline);
  }

  /**
   * Filter log entries
   */
  filterLogs(filter: {
    level?: 'debug' | 'info' | 'warn' | 'error';
    startTime?: number;
    endTime?: number;
    message?: string;
  }): LogEntry[] {
    return this.logEntries.filter(entry => {
      if (filter.level && entry.level !== filter.level) return false;
      if (filter.startTime && entry.timestamp < filter.startTime) return false;
      if (filter.endTime && entry.timestamp > filter.endTime) return false;
      if (filter.message && !entry.message.toLowerCase().includes(filter.message.toLowerCase())) return false;
      return true;
    });
  }

  /**
   * Get log statistics
   */
  getLogStatistics(): {
    totalEntries: number;
    entriesByLevel: Record<string, number>;
    timeRange: { start: number; end: number } | null;
    averageProcessingTime: number;
  } {
    const entriesByLevel = this.logEntries.reduce((acc, entry) => {
      acc[entry.level] = (acc[entry.level] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const processingTimes = this.logEntries
      .filter(entry => entry.performance?.processingTime)
      .map(entry => entry.performance!.processingTime);

    const averageProcessingTime = processingTimes.length > 0
      ? processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length
      : 0;

    const timeRange = this.logEntries.length > 0
      ? {
          start: this.logEntries[0].timestamp,
          end: this.logEntries[this.logEntries.length - 1].timestamp
        }
      : null;

    return {
      totalEntries: this.logEntries.length,
      entriesByLevel,
      timeRange,
      averageProcessingTime
    };
  }

  /**
   * Export logs as JSON
   */
  exportLogs(): string {
    const exportData = {
      timestamp: new Date().toISOString(),
      config: this.config,
      statistics: this.getLogStatistics(),
      timeline: this.getTimeline(),
      logs: this.logEntries,
      connectionState: this.store.getConnectionState(),
      performance: this.store.getState().performance
    };

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * Clear all logs
   */
  clearLogs(): void {
    this.logEntries = [];
    this.info('Logs cleared');
  }

  /**
   * Set log level
   */
  setLogLevel(level: 'debug' | 'info' | 'warn' | 'error'): void {
    this.config.logLevel = level;
    this.info(`Log level set to ${level}`);
  }

  /**
   * Enable/disable console output
   */
  setConsoleOutput(enabled: boolean): void {
    this.config.enableConsoleOutput = enabled;
    this.info(`Console output ${enabled ? 'enabled' : 'disabled'}`);
  }
}
