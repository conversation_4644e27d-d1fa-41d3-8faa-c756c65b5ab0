/**
 * Recovery Manager - Handles connection recovery with exponential backoff
 * Manages subscription teardown and recreation after connection loss
 */

import { SupabaseClient, RealtimeChannel } from '@supabase/supabase-js';
import { DebugStateStore } from './DebugStateStore';
import { EventLogger } from './EventLogger';

export interface RecoveryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  jitterFactor: number;
}

export interface RecoveryAttempt {
  attempt: number;
  timestamp: number;
  delay: number;
  success: boolean;
  error?: Error;
  duration?: number;
}

export class RecoveryManager {
  private supabase: SupabaseClient;
  private store: DebugStateStore;
  private logger: EventLogger;
  private config: RecoveryConfig;
  
  private isRecovering = false;
  private recoveryAttempts: RecoveryAttempt[] = [];
  private recoveryTimer: NodeJS.Timeout | null = null;
  private channelRegistry = new Map<string, RealtimeChannel>();
  private subscriptionCallbacks = new Map<string, Function[]>();

  constructor(
    supabase: SupabaseClient,
    store: DebugStateStore,
    logger: EventLogger,
    config: Partial<RecoveryConfig> = {}
  ) {
    this.supabase = supabase;
    this.store = store;
    this.logger = logger;
    this.config = {
      maxRetries: 5,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffMultiplier: 2,
      jitterFactor: 0.3,
      ...config
    };
  }

  async initialize(): Promise<void> {
    this.logger.info('Recovery Manager initialized');
    
    // Monitor connection state changes
    this.store.subscribe((state) => {
      const wasConnected = this.isConnected();
      const isConnected = state.connection.isConnected;
      
      // Trigger recovery if connection was lost
      if (wasConnected && !isConnected && !this.isRecovering) {
        this.logger.warn('Connection lost, starting recovery');
        this.startRecovery();
      }
    });
  }

  /**
   * Start connection recovery process
   */
  async startRecovery(): Promise<void> {
    if (this.isRecovering) {
      this.logger.debug('Recovery already in progress');
      return;
    }

    this.isRecovering = true;
    this.recoveryAttempts = [];
    
    this.logger.info('Starting connection recovery');
    this.store.updateConnectionState({ 
      reconnectAttempts: 0,
      lastError: null 
    });

    await this.attemptRecovery();
  }

  /**
   * Attempt recovery with exponential backoff
   */
  private async attemptRecovery(): Promise<void> {
    const attemptNumber = this.recoveryAttempts.length + 1;
    
    if (attemptNumber > this.config.maxRetries) {
      this.logger.error('Max recovery attempts reached, giving up');
      this.isRecovering = false;
      this.store.updateConnectionState({
        lastError: new Error('Max recovery attempts exceeded')
      });
      return;
    }

    const delay = this.calculateBackoffDelay(attemptNumber);
    
    this.logger.info(`Recovery attempt ${attemptNumber}/${this.config.maxRetries} in ${delay}ms`);
    
    const attempt: RecoveryAttempt = {
      attempt: attemptNumber,
      timestamp: Date.now(),
      delay,
      success: false
    };

    this.store.updateConnectionState({ 
      reconnectAttempts: attemptNumber 
    });

    // Wait for backoff delay
    await this.sleep(delay);

    const startTime = Date.now();
    
    try {
      // Attempt to recover connection
      await this.performRecovery();
      
      attempt.success = true;
      attempt.duration = Date.now() - startTime;
      
      this.logger.info(`Recovery attempt ${attemptNumber} succeeded in ${attempt.duration}ms`);
      this.isRecovering = false;
      
      this.store.updateConnectionState({
        isConnected: true,
        lastError: null
      });
      
      // Track successful recovery time
      this.store.addPerformanceMetric('reconnectionTimes', attempt.duration);
      
    } catch (error) {
      attempt.error = error as Error;
      attempt.duration = Date.now() - startTime;
      
      this.logger.warn(`Recovery attempt ${attemptNumber} failed: ${error}`, error);
      
      this.store.updateConnectionState({
        lastError: error as Error
      });
      
      // Schedule next attempt
      this.recoveryTimer = setTimeout(() => {
        this.attemptRecovery();
      }, 100);
    }
    
    this.recoveryAttempts.push(attempt);
    
    this.store.addTimelineEntry({
      id: this.generateEventId(),
      timestamp: Date.now(),
      type: 'connection',
      event: 'recovery_attempt',
      data: attempt,
      context: this.getCurrentContext()
    });
  }

  /**
   * Perform the actual recovery steps
   */
  private async performRecovery(): Promise<void> {
    this.logger.debug('Performing recovery steps');
    
    // Step 1: Check auth state
    await this.recoverAuthState();
    
    // Step 2: Recreate channels
    await this.recreateChannels();
    
    // Step 3: Verify connection health
    await this.verifyConnectionHealth();
    
    this.logger.info('Recovery completed successfully');
  }

  /**
   * Recover authentication state
   */
  private async recoverAuthState(): Promise<void> {
    this.logger.debug('Recovering auth state');
    
    try {
      const { data: { session }, error } = await this.supabase.auth.getSession();
      
      if (error) {
        throw new Error(`Auth recovery failed: ${error.message}`);
      }
      
      if (session) {
        this.logger.info('Auth state recovered successfully');
        this.store.updateConnectionState({ authState: 'authenticated' });
      } else {
        this.logger.warn('No active session found');
        this.store.updateConnectionState({ authState: 'unauthenticated' });
      }
      
    } catch (error) {
      this.logger.error('Auth state recovery failed', error);
      throw error;
    }
  }

  /**
   * Recreate all registered channels
   */
  private async recreateChannels(): Promise<void> {
    this.logger.debug('Recreating channels');
    
    const channelPromises = Array.from(this.channelRegistry.entries()).map(
      async ([channelId, channel]) => {
        try {
          // Remove old channel
          this.supabase.removeChannel(channel);
          
          // Create new channel with same configuration
          const newChannel = await this.recreateChannel(channelId);
          this.channelRegistry.set(channelId, newChannel);
          
          this.logger.debug(`Channel ${channelId} recreated successfully`);
          
        } catch (error) {
          this.logger.error(`Failed to recreate channel ${channelId}`, error);
          throw error;
        }
      }
    );
    
    await Promise.all(channelPromises);
    this.logger.info(`Recreated ${channelPromises.length} channels`);
  }

  /**
   * Recreate a specific channel
   */
  private async recreateChannel(channelId: string): Promise<RealtimeChannel> {
    // This would need to be implemented based on how channels are tracked
    // For now, return a placeholder
    throw new Error('Channel recreation not implemented');
  }

  /**
   * Verify connection health after recovery
   */
  private async verifyConnectionHealth(): Promise<void> {
    this.logger.debug('Verifying connection health');
    
    // Send a test message to verify connection
    const testChannel = this.supabase.channel('health_check');
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.supabase.removeChannel(testChannel);
        reject(new Error('Connection health check timeout'));
      }, 5000);
      
      testChannel.subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          clearTimeout(timeout);
          this.supabase.removeChannel(testChannel);
          this.logger.info('Connection health verified');
          resolve();
        } else if (status === 'CHANNEL_ERROR') {
          clearTimeout(timeout);
          this.supabase.removeChannel(testChannel);
          reject(new Error('Connection health check failed'));
        }
      });
    });
  }

  /**
   * Force reconnection (manual trigger)
   */
  async forceReconnect(): Promise<void> {
    this.logger.info('Force reconnect triggered');
    
    // Stop current recovery if in progress
    if (this.recoveryTimer) {
      clearTimeout(this.recoveryTimer);
      this.recoveryTimer = null;
    }
    
    this.isRecovering = false;
    
    // Mark as disconnected to trigger recovery
    this.store.updateConnectionState({ 
      isConnected: false,
      lastError: new Error('Manual reconnect triggered')
    });
    
    // Start recovery immediately
    await this.startRecovery();
  }

  /**
   * Calculate exponential backoff delay with jitter
   */
  private calculateBackoffDelay(attempt: number): number {
    const exponentialDelay = this.config.baseDelay * Math.pow(this.config.backoffMultiplier, attempt - 1);
    const cappedDelay = Math.min(exponentialDelay, this.config.maxDelay);
    
    // Add jitter to prevent thundering herd
    const jitter = cappedDelay * this.config.jitterFactor * Math.random();
    
    return Math.floor(cappedDelay + jitter);
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Check if currently connected
   */
  private isConnected(): boolean {
    return this.store.getConnectionState().isConnected;
  }

  /**
   * Get current context
   */
  private getCurrentContext() {
    const connectionState = this.store.getConnectionState();
    
    return {
      visibilityState: document.visibilityState,
      connectionState: connectionState.isConnected ? 'connected' as const : 'disconnected' as const,
      authState: connectionState.authState,
      activeChannels: connectionState.activeChannels.length
    };
  }

  /**
   * Generate event ID
   */
  private generateEventId(): string {
    return `recovery_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get recovery statistics
   */
  getRecoveryStatistics(): {
    totalAttempts: number;
    successfulAttempts: number;
    averageRecoveryTime: number;
    isCurrentlyRecovering: boolean;
  } {
    const successfulAttempts = this.recoveryAttempts.filter(a => a.success);
    const recoveryTimes = successfulAttempts
      .filter(a => a.duration)
      .map(a => a.duration!);
    
    return {
      totalAttempts: this.recoveryAttempts.length,
      successfulAttempts: successfulAttempts.length,
      averageRecoveryTime: recoveryTimes.length > 0 
        ? recoveryTimes.reduce((a, b) => a + b, 0) / recoveryTimes.length 
        : 0,
      isCurrentlyRecovering: this.isRecovering
    };
  }

  /**
   * Cleanup
   */
  destroy(): void {
    this.logger.info('Destroying Recovery Manager');
    
    if (this.recoveryTimer) {
      clearTimeout(this.recoveryTimer);
      this.recoveryTimer = null;
    }
    
    this.isRecovering = false;
    this.channelRegistry.clear();
    this.subscriptionCallbacks.clear();
    this.recoveryAttempts = [];
  }
}
