/**
 * Integration test for Supabase Debug Console
 * Run this in browser console to verify functionality
 */

export async function testDebugConsole() {
  console.log('🧪 Testing Supabase Debug Console...');
  
  try {
    // Check if debug console is available
    const debugInstance = (window as any).debugSupabase;
    if (!debugInstance) {
      throw new Error('Debug console not found. Make sure you\'re in development mode.');
    }
    
    console.log('✅ Debug console found');
    
    // Test connection state
    const connectionState = debugInstance.getConnectionState();
    console.log('📊 Connection State:', connectionState);
    
    // Test timeline
    const timeline = debugInstance.getEventTimeline();
    console.log('📈 Event Timeline:', timeline.length, 'events');
    
    // Test force reconnect
    console.log('🔄 Testing force reconnect...');
    await debugInstance.forceReconnect();
    console.log('✅ Force reconnect completed');
    
    // Test scenario (if testing is enabled)
    if (debugInstance.testing) {
      console.log('🧪 Testing tab switching scenario...');
      const result = await debugInstance.runScenario('tab_switching');
      console.log('📊 Scenario Result:', result);
    }
    
    // Test log export
    const logs = debugInstance.exportLogs();
    const parsed = JSON.parse(logs);
    console.log('📄 Exported logs:', parsed.timeline?.length || 0, 'events');
    
    console.log('🎉 All tests passed! Debug console is working correctly.');
    
    return {
      success: true,
      connectionState,
      timelineEvents: timeline.length,
      exportedEvents: parsed.timeline?.length || 0
    };
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Auto-run test in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Wait for debug console to initialize
  setTimeout(() => {
    if ((window as any).debugSupabase) {
      console.log('🚀 Supabase Debug Console is ready!');
      console.log('💡 Run testDebugConsole() to verify functionality');
      console.log('🎛️ Access debug instance via window.debugSupabase');
      
      // Make test function available globally
      (window as any).testDebugConsole = testDebugConsole;
    }
  }, 2000);
}
