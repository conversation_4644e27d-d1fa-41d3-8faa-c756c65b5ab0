/**
 * Comprehensive test suite for Supabase Debug Console scenarios
 * Tests browser refresh, tab switching, network transitions, and auth flows
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { createClient } from '@supabase/supabase-js';
import { debugSupabase } from '../index';
import { <PERSON><PERSON>rioRunner } from '../testing/ScenarioRunner';
import { DebugStateStore } from '../core/DebugStateStore';
import { EventLogger } from '../core/EventLogger';

// Mock Supabase client
const mockSupabaseClient = createClient('http://localhost:54321', 'test-key');

// Mock DOM APIs
Object.defineProperty(document, 'visibilityState', {
  writable: true,
  value: 'visible'
});

Object.defineProperty(window, 'WebSocket', {
  writable: true,
  value: class MockWebSocket {
    static CONNECTING = 0;
    static OPEN = 1;
    static CLOSING = 2;
    static CLOSED = 3;

    readyState = MockWebSocket.CONNECTING;
    url: string;
    onopen: ((event: Event) => void) | null = null;
    onclose: ((event: CloseEvent) => void) | null = null;
    onerror: ((event: Event) => void) | null = null;
    onmessage: ((event: MessageEvent) => void) | null = null;

    constructor(url: string) {
      this.url = url;
      setTimeout(() => {
        this.readyState = MockWebSocket.OPEN;
        this.onopen?.(new Event('open'));
      }, 10);
    }

    close() {
      this.readyState = MockWebSocket.CLOSED;
      this.onclose?.(new CloseEvent('close'));
    }

    send(data: string) {
      // Mock sending data
    }

    addEventListener(type: string, listener: EventListener) {
      if (type === 'open') this.onopen = listener as any;
      if (type === 'close') this.onclose = listener as any;
      if (type === 'error') this.onerror = listener as any;
      if (type === 'message') this.onmessage = listener as any;
    }
  }
});

describe('Supabase Debug Console - Scenario Tests', () => {
  let debugInstance: any;
  let scenarioRunner: ScenarioRunner;
  let store: DebugStateStore;
  let logger: EventLogger;

  beforeEach(async () => {
    // Initialize debug console
    debugInstance = await debugSupabase.init({
      supabaseClient: mockSupabaseClient,
      enableDashboard: false, // Disable UI in tests
      enableTesting: true,
      enableLogging: true,
      logLevel: 'debug'
    });

    scenarioRunner = debugInstance.testing;
    store = debugInstance.store;
    logger = debugInstance.logger;

    // Reset state
    store.clear();
  });

  afterEach(() => {
    debugSupabase.destroy();
    jest.clearAllMocks();
  });

  describe('Browser Refresh Scenario', () => {
    it('should handle browser refresh and restore connection', async () => {
      // Run browser refresh scenario
      const result = await scenarioRunner.runScenario('browser_refresh');

      expect(result.success).toBe(true);
      expect(result.error).toBeNull();
      expect(result.metrics.duration).toBeLessThan(30000); // Should complete within 30s

      // Verify connection was restored
      const connectionState = store.getConnectionState();
      expect(connectionState.isConnected).toBe(true);
    }, 35000);

    it('should track visibility changes during refresh', async () => {
      await scenarioRunner.runScenario('browser_refresh');

      const timeline = logger.getTimeline();
      const visibilityEvents = timeline.filter(e => e.type === 'visibility');

      expect(visibilityEvents.length).toBeGreaterThan(0);
      expect(visibilityEvents.some(e => e.event === 'visibility_change')).toBe(true);
    });

    it('should meet performance requirements', async () => {
      const startTime = Date.now();
      const result = await scenarioRunner.runScenario('browser_refresh');
      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(30000); // 30 second timeout
      expect(result.success).toBe(true);

      // Check state update latency
      const performance = store.getState().performance;
      const avgLatency = performance.stateUpdateLatency.reduce((a, b) => a + b, 0) / performance.stateUpdateLatency.length;
      expect(avgLatency).toBeLessThan(200); // <200ms requirement
    });
  });

  describe('Tab Switching Scenario', () => {
    it('should handle tab switching gracefully', async () => {
      const result = await scenarioRunner.runScenario('tab_switching');

      expect(result.success).toBe(true);
      expect(result.error).toBeNull();

      // Verify connection is maintained or restored
      const connectionState = store.getConnectionState();
      expect(connectionState.isConnected).toBe(true);
    }, 25000);

    it('should track tab visibility states', async () => {
      await scenarioRunner.runScenario('tab_switching');

      const timeline = logger.getTimeline();
      const visibilityEvents = timeline.filter(e => e.type === 'visibility');

      // Should have both hidden and visible events
      const contexts = visibilityEvents.map(e => e.context.visibilityState);
      expect(contexts).toContain('hidden');
      expect(contexts).toContain('visible');
    });
  });

  describe('Network Transition Scenario', () => {
    it('should recover from network offline/online transitions', async () => {
      const result = await scenarioRunner.runScenario('network_transition');

      expect(result.success).toBe(true);
      expect(result.error).toBeNull();

      // Verify connection was restored after coming back online
      const connectionState = store.getConnectionState();
      expect(connectionState.isConnected).toBe(true);
    }, 30000);

    it('should track network state changes', async () => {
      // Mock network events
      const originalDispatchEvent = window.dispatchEvent;
      const dispatchedEvents: string[] = [];
      
      window.dispatchEvent = jest.fn((event: Event) => {
        dispatchedEvents.push(event.type);
        return originalDispatchEvent.call(window, event);
      });

      await scenarioRunner.runScenario('network_transition');

      expect(dispatchedEvents).toContain('offline');
      expect(dispatchedEvents).toContain('online');

      window.dispatchEvent = originalDispatchEvent;
    });
  });

  describe('Token Expiration Scenario', () => {
    it('should handle auth token expiration and renewal', async () => {
      const result = await scenarioRunner.runScenario('token_expiration');

      expect(result.success).toBe(true);
      expect(result.error).toBeNull();

      // Verify auth state is maintained
      const connectionState = store.getConnectionState();
      expect(connectionState.authState).toBe('authenticated');
      expect(connectionState.isConnected).toBe(true);
    }, 40000);

    it('should track auth state transitions', async () => {
      await scenarioRunner.runScenario('token_expiration');

      const timeline = logger.getTimeline();
      const authEvents = timeline.filter(e => e.type === 'auth');

      expect(authEvents.length).toBeGreaterThan(0);
      
      // Should track refreshing state
      const authStates = authEvents.map(e => e.context.authState);
      expect(authStates).toContain('refreshing');
      expect(authStates).toContain('authenticated');
    });
  });

  describe('Multiple Channels Scenario', () => {
    it('should handle multiple concurrent channel subscriptions', async () => {
      const result = await scenarioRunner.runScenario('multiple_channels');

      expect(result.success).toBe(true);
      expect(result.error).toBeNull();

      // Verify multiple channels are active
      const connectionState = store.getConnectionState();
      expect(connectionState.activeChannels.length).toBeGreaterThanOrEqual(3);
      expect(connectionState.isConnected).toBe(true);
    }, 25000);

    it('should track channel lifecycle events', async () => {
      await scenarioRunner.runScenario('multiple_channels');

      const timeline = logger.getTimeline();
      const channelEvents = timeline.filter(e => e.type === 'channel');

      expect(channelEvents.length).toBeGreaterThan(0);
      
      // Should have subscription events
      const subscriptionEvents = channelEvents.filter(e => e.event === 'subscription_status');
      expect(subscriptionEvents.length).toBeGreaterThan(0);
    });
  });

  describe('Performance Requirements', () => {
    it('should meet state update latency requirements', async () => {
      // Run a scenario to generate state updates
      await scenarioRunner.runScenario('tab_switching');

      const performance = store.getState().performance;
      
      // Check that all state updates are under 200ms
      performance.stateUpdateLatency.forEach(latency => {
        expect(latency).toBeLessThan(200);
      });

      // Check average latency
      if (performance.stateUpdateLatency.length > 0) {
        const avgLatency = performance.stateUpdateLatency.reduce((a, b) => a + b, 0) / performance.stateUpdateLatency.length;
        expect(avgLatency).toBeLessThan(100); // Even stricter for average
      }
    });

    it('should track memory usage within limits', async () => {
      await scenarioRunner.runScenario('multiple_channels');

      const performance = store.getState().performance;
      
      // Memory usage should be reasonable (less than 50MB for debug console)
      if (performance.memoryUsage > 0) {
        expect(performance.memoryUsage).toBeLessThan(50 * 1024 * 1024);
      }
    });

    it('should process events efficiently', async () => {
      await scenarioRunner.runScenario('browser_refresh');

      const performance = store.getState().performance;
      
      // Event processing should be fast
      performance.eventProcessingTime.forEach(time => {
        expect(time).toBeLessThan(50); // 50ms max per event
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle scenario failures gracefully', async () => {
      // Mock a scenario that will fail
      scenarioRunner.registerScenario({
        name: 'failing_test',
        description: 'Test scenario that fails',
        timeout: 5000,
        expectedOutcome: 'Should fail',
        steps: [
          {
            name: 'fail_step',
            action: async () => {
              throw new Error('Intentional test failure');
            }
          }
        ]
      });

      const result = await scenarioRunner.runScenario('failing_test');

      expect(result.success).toBe(false);
      expect(result.error).toBeTruthy();
      expect(result.error?.message).toBe('Intentional test failure');
    });

    it('should handle timeout scenarios', async () => {
      // Mock a scenario that times out
      scenarioRunner.registerScenario({
        name: 'timeout_test',
        description: 'Test scenario that times out',
        timeout: 100, // Very short timeout
        expectedOutcome: 'Should timeout',
        steps: [
          {
            name: 'slow_step',
            action: async () => {
              await new Promise(resolve => setTimeout(resolve, 200)); // Longer than timeout
            }
          }
        ]
      });

      const result = await scenarioRunner.runScenario('timeout_test');

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('timed out');
    });
  });

  describe('Data Export', () => {
    it('should export logs in valid JSON format', async () => {
      await scenarioRunner.runScenario('tab_switching');

      const exportedLogs = debugInstance.exportLogs();
      
      expect(() => JSON.parse(exportedLogs)).not.toThrow();
      
      const parsed = JSON.parse(exportedLogs);
      expect(parsed).toHaveProperty('timestamp');
      expect(parsed).toHaveProperty('connectionState');
      expect(parsed).toHaveProperty('timeline');
      expect(parsed).toHaveProperty('config');
    });

    it('should include performance metrics in export', async () => {
      await scenarioRunner.runScenario('browser_refresh');

      const exportedLogs = debugInstance.exportLogs();
      const parsed = JSON.parse(exportedLogs);
      
      expect(parsed.connectionState).toHaveProperty('isConnected');
      expect(parsed.timeline).toBeInstanceOf(Array);
      expect(parsed.timeline.length).toBeGreaterThan(0);
    });
  });
});
