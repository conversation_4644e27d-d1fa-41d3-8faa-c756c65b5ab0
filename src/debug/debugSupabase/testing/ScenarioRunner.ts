/**
 * Sc<PERSON>rio Runner - Automated testing for connection scenarios
 * Tests browser refresh, tab switching, network transitions, and auth flows
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { DebugStateStore, ScenarioResult } from '../core/DebugStateStore';
import { EventLogger } from '../core/EventLogger';

export interface TestScenario {
  name: string;
  description: string;
  steps: TestStep[];
  timeout: number;
  expectedOutcome: string;
}

export interface TestStep {
  name: string;
  action: () => Promise<void>;
  validation?: () => Promise<boolean>;
  timeout?: number;
}

export interface ScenarioContext {
  supabase: SupabaseClient;
  store: DebugStateStore;
  logger: EventLogger;
  startTime: number;
  stepResults: Array<{ step: string; success: boolean; duration: number; error?: Error }>;
}

export class ScenarioRunner {
  private supabase: SupabaseClient;
  private store: DebugStateStore;
  private logger: EventLogger;
  private scenarios: Map<string, TestScenario> = new Map();
  private isRunning = false;

  constructor(
    supabase: SupabaseClient,
    store: DebugStateStore,
    logger: EventLogger
  ) {
    this.supabase = supabase;
    this.store = store;
    this.logger = logger;
  }

  async initialize(): Promise<void> {
    this.logger.info('Initializing Scenario Runner');
    this.registerDefaultScenarios();
  }

  /**
   * Register default test scenarios
   */
  private registerDefaultScenarios(): void {
    // Browser refresh scenario
    this.registerScenario({
      name: 'browser_refresh',
      description: 'Test connection recovery after browser refresh',
      timeout: 30000,
      expectedOutcome: 'Connection restored with all subscriptions active',
      steps: [
        {
          name: 'establish_connection',
          action: async () => {
            await this.establishTestConnection();
          },
          validation: async () => {
            return this.store.getConnectionState().isConnected;
          }
        },
        {
          name: 'simulate_refresh',
          action: async () => {
            await this.simulateBrowserRefresh();
          }
        },
        {
          name: 'verify_recovery',
          action: async () => {
            await this.waitForRecovery();
          },
          validation: async () => {
            const state = this.store.getConnectionState();
            return state.isConnected && state.activeChannels.length > 0;
          }
        }
      ]
    });

    // Tab switching scenario
    this.registerScenario({
      name: 'tab_switching',
      description: 'Test connection behavior during tab switching',
      timeout: 20000,
      expectedOutcome: 'Connection maintained or quickly restored when tab becomes active',
      steps: [
        {
          name: 'establish_connection',
          action: async () => {
            await this.establishTestConnection();
          }
        },
        {
          name: 'simulate_tab_hidden',
          action: async () => {
            await this.simulateTabHidden();
          }
        },
        {
          name: 'wait_background_period',
          action: async () => {
            await this.sleep(5000); // 5 seconds in background
          }
        },
        {
          name: 'simulate_tab_visible',
          action: async () => {
            await this.simulateTabVisible();
          }
        },
        {
          name: 'verify_connection',
          action: async () => {
            await this.waitForRecovery();
          },
          validation: async () => {
            return this.store.getConnectionState().isConnected;
          }
        }
      ]
    });

    // Network offline/online scenario
    this.registerScenario({
      name: 'network_transition',
      description: 'Test connection recovery during network offline/online transitions',
      timeout: 25000,
      expectedOutcome: 'Connection automatically restored when network comes back online',
      steps: [
        {
          name: 'establish_connection',
          action: async () => {
            await this.establishTestConnection();
          }
        },
        {
          name: 'simulate_offline',
          action: async () => {
            await this.simulateNetworkOffline();
          }
        },
        {
          name: 'wait_offline_period',
          action: async () => {
            await this.sleep(3000);
          }
        },
        {
          name: 'simulate_online',
          action: async () => {
            await this.simulateNetworkOnline();
          }
        },
        {
          name: 'verify_recovery',
          action: async () => {
            await this.waitForRecovery();
          },
          validation: async () => {
            return this.store.getConnectionState().isConnected;
          }
        }
      ]
    });

    // Auth token expiration scenario
    this.registerScenario({
      name: 'token_expiration',
      description: 'Test connection behavior during auth token expiration and renewal',
      timeout: 35000,
      expectedOutcome: 'Connection maintained through token refresh',
      steps: [
        {
          name: 'establish_authenticated_connection',
          action: async () => {
            await this.establishAuthenticatedConnection();
          }
        },
        {
          name: 'simulate_token_expiry',
          action: async () => {
            await this.simulateTokenExpiry();
          }
        },
        {
          name: 'trigger_token_refresh',
          action: async () => {
            await this.triggerTokenRefresh();
          }
        },
        {
          name: 'verify_connection_maintained',
          action: async () => {
            await this.waitForRecovery();
          },
          validation: async () => {
            const state = this.store.getConnectionState();
            return state.isConnected && state.authState === 'authenticated';
          }
        }
      ]
    });

    // Multiple concurrent channels scenario
    this.registerScenario({
      name: 'multiple_channels',
      description: 'Test multiple concurrent realtime channel subscriptions',
      timeout: 20000,
      expectedOutcome: 'All channels maintain subscriptions and receive events',
      steps: [
        {
          name: 'create_multiple_channels',
          action: async () => {
            await this.createMultipleChannels();
          }
        },
        {
          name: 'verify_all_subscribed',
          action: async () => {
            await this.verifyAllChannelsSubscribed();
          },
          validation: async () => {
            return this.store.getConnectionState().activeChannels.length >= 3;
          }
        },
        {
          name: 'simulate_connection_loss',
          action: async () => {
            await this.simulateConnectionLoss();
          }
        },
        {
          name: 'verify_all_recovered',
          action: async () => {
            await this.waitForRecovery();
          },
          validation: async () => {
            const state = this.store.getConnectionState();
            return state.isConnected && state.activeChannels.length >= 3;
          }
        }
      ]
    });

    this.logger.info(`Registered ${this.scenarios.size} test scenarios`);
  }

  /**
   * Register a custom test scenario
   */
  registerScenario(scenario: TestScenario): void {
    this.scenarios.set(scenario.name, scenario);
    this.logger.debug(`Registered scenario: ${scenario.name}`);
  }

  /**
   * Run a specific scenario
   */
  async runScenario(scenarioName: string): Promise<ScenarioResult> {
    if (this.isRunning) {
      throw new Error('Another scenario is already running');
    }

    const scenario = this.scenarios.get(scenarioName);
    if (!scenario) {
      throw new Error(`Scenario '${scenarioName}' not found`);
    }

    this.isRunning = true;
    this.store.setActiveScenario(scenarioName);
    
    const startTime = Date.now();
    this.logger.info(`Running scenario: ${scenario.name}`);

    const context: ScenarioContext = {
      supabase: this.supabase,
      store: this.store,
      logger: this.logger,
      startTime,
      stepResults: []
    };

    try {
      // Run all steps
      for (const step of scenario.steps) {
        await this.runStep(step, context, scenario.timeout);
      }

      const result: ScenarioResult = {
        scenario: scenarioName,
        startTime,
        endTime: Date.now(),
        success: true,
        error: null,
        metrics: {
          duration: Date.now() - startTime,
          steps: context.stepResults
        }
      };

      this.logger.info(`Scenario '${scenarioName}' completed successfully`);
      this.store.addScenarioResult(result);
      return result;

    } catch (error) {
      const result: ScenarioResult = {
        scenario: scenarioName,
        startTime,
        endTime: Date.now(),
        success: false,
        error: error as Error,
        metrics: {
          duration: Date.now() - startTime,
          steps: context.stepResults
        }
      };

      this.logger.error(`Scenario '${scenarioName}' failed`, error);
      this.store.addScenarioResult(result);
      return result;

    } finally {
      this.isRunning = false;
      this.store.setActiveScenario(null);
    }
  }

  /**
   * Run a single test step
   */
  private async runStep(step: TestStep, context: ScenarioContext, globalTimeout: number): Promise<void> {
    const stepStartTime = Date.now();
    const timeout = step.timeout || globalTimeout;

    this.logger.debug(`Running step: ${step.name}`);

    try {
      // Run step action with timeout
      await Promise.race([
        step.action(),
        this.createTimeoutPromise(timeout, `Step '${step.name}' timed out`)
      ]);

      // Run validation if provided
      if (step.validation) {
        const isValid = await Promise.race([
          step.validation(),
          this.createTimeoutPromise(timeout, `Validation for '${step.name}' timed out`)
        ]);

        if (!isValid) {
          throw new Error(`Validation failed for step '${step.name}'`);
        }
      }

      const duration = Date.now() - stepStartTime;
      context.stepResults.push({
        step: step.name,
        success: true,
        duration
      });

      this.logger.debug(`Step '${step.name}' completed in ${duration}ms`);

    } catch (error) {
      const duration = Date.now() - stepStartTime;
      context.stepResults.push({
        step: step.name,
        success: false,
        duration,
        error: error as Error
      });

      throw error;
    }
  }

  /**
   * Create a timeout promise
   */
  private createTimeoutPromise(timeout: number, message: string): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => reject(new Error(message)), timeout);
    });
  }

  /**
   * Test scenario implementations
   */
  private async establishTestConnection(): Promise<void> {
    // Create a test channel to establish connection
    const channel = this.supabase.channel('test_connection');
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Failed to establish test connection'));
      }, 10000);

      channel.subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          clearTimeout(timeout);
          resolve();
        } else if (status === 'CHANNEL_ERROR') {
          clearTimeout(timeout);
          reject(new Error('Channel subscription failed'));
        }
      });
    });
  }

  private async simulateBrowserRefresh(): Promise<void> {
    // Simulate browser refresh by triggering visibility change and connection loss
    this.dispatchVisibilityChange('hidden');
    await this.sleep(100);
    
    // Simulate connection loss
    this.store.updateConnectionState({ isConnected: false });
    await this.sleep(500);
    
    // Simulate page becoming visible again
    this.dispatchVisibilityChange('visible');
  }

  private async simulateTabHidden(): Promise<void> {
    this.dispatchVisibilityChange('hidden');
  }

  private async simulateTabVisible(): Promise<void> {
    this.dispatchVisibilityChange('visible');
  }

  private async simulateNetworkOffline(): Promise<void> {
    // Dispatch offline event
    window.dispatchEvent(new Event('offline'));
    this.store.updateConnectionState({ isConnected: false });
  }

  private async simulateNetworkOnline(): Promise<void> {
    // Dispatch online event
    window.dispatchEvent(new Event('online'));
  }

  private async establishAuthenticatedConnection(): Promise<void> {
    // This would need actual auth implementation
    this.store.updateConnectionState({ authState: 'authenticated' });
    await this.establishTestConnection();
  }

  private async simulateTokenExpiry(): Promise<void> {
    this.store.updateConnectionState({ authState: 'refreshing' });
  }

  private async triggerTokenRefresh(): Promise<void> {
    // Simulate token refresh
    await this.sleep(1000);
    this.store.updateConnectionState({ authState: 'authenticated' });
  }

  private async createMultipleChannels(): Promise<void> {
    const channels = ['channel1', 'channel2', 'channel3'];
    
    for (const channelName of channels) {
      const channel = this.supabase.channel(channelName);
      await new Promise<void>((resolve) => {
        channel.subscribe((status) => {
          if (status === 'SUBSCRIBED') {
            resolve();
          }
        });
      });
    }
  }

  private async verifyAllChannelsSubscribed(): Promise<void> {
    // Wait for all channels to be subscribed
    await this.sleep(1000);
  }

  private async simulateConnectionLoss(): Promise<void> {
    this.store.updateConnectionState({ isConnected: false });
  }

  private async waitForRecovery(): Promise<void> {
    // Wait for connection to be restored
    const maxWait = 10000; // 10 seconds
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWait) {
      if (this.store.getConnectionState().isConnected) {
        return;
      }
      await this.sleep(100);
    }
    
    throw new Error('Recovery timeout');
  }

  /**
   * Utility methods
   */
  private dispatchVisibilityChange(state: 'visible' | 'hidden'): void {
    Object.defineProperty(document, 'visibilityState', {
      value: state,
      writable: true
    });
    
    document.dispatchEvent(new Event('visibilitychange'));
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get available scenarios
   */
  getAvailableScenarios(): string[] {
    return Array.from(this.scenarios.keys());
  }

  /**
   * Get scenario details
   */
  getScenarioDetails(scenarioName: string): TestScenario | null {
    return this.scenarios.get(scenarioName) || null;
  }

  /**
   * Cleanup
   */
  destroy(): void {
    this.logger.info('Destroying Scenario Runner');
    this.isRunning = false;
    this.scenarios.clear();
  }
}
