/**
 * Simplified Debug Console for troubleshooting
 * Minimal implementation to identify issues
 */

import { SupabaseClient } from '@supabase/supabase-js';

interface SimpleDebugConfig {
  supabaseClient: SupabaseClient;
  enableLogging?: boolean;
}

class SimpleDebugConsole {
  private supabase: SupabaseClient;
  private isInitialized = false;

  constructor(config: SimpleDebugConfig) {
    this.supabase = config.supabaseClient;
    console.log('🔧 Simple Debug Console created');
  }

  async initialize(): Promise<void> {
    try {
      console.log('🚀 Initializing Simple Debug Console...');
      
      // Basic connection test
      const { data, error } = await this.supabase.auth.getSession();
      console.log('📊 Auth session check:', { hasSession: !!data.session, error });
      
      // Test channel creation
      const testChannel = this.supabase.channel('debug_test');
      console.log('📡 Test channel created:', testChannel);
      
      this.isInitialized = true;
      console.log('✅ Simple Debug Console initialized successfully');
      
    } catch (error) {
      console.error('❌ Simple Debug Console initialization failed:', error);
      throw error;
    }
  }

  getStatus() {
    return {
      initialized: this.isInitialized,
      timestamp: new Date().toISOString(),
      supabaseUrl: this.supabase.supabaseUrl
    };
  }

  testConnection() {
    console.log('🧪 Testing Supabase connection...');
    
    // Test basic functionality
    const channel = this.supabase.channel('connection_test');
    
    channel.subscribe((status) => {
      console.log('📊 Channel subscription status:', status);
    });
    
    setTimeout(() => {
      this.supabase.removeChannel(channel);
      console.log('🧹 Test channel cleaned up');
    }, 5000);
  }
}

// Simple initialization function
export async function initSimpleDebug(config: SimpleDebugConfig) {
  try {
    const debugConsole = new SimpleDebugConsole(config);
    await debugConsole.initialize();
    
    // Make available globally
    if (typeof window !== 'undefined') {
      (window as any).simpleDebug = debugConsole;
      console.log('🌐 Simple debug available at window.simpleDebug');
    }
    
    return debugConsole;
    
  } catch (error) {
    console.error('💥 Failed to initialize simple debug:', error);
    throw error;
  }
}
