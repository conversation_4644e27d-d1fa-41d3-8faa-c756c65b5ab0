/**
 * Connection Panel - Displays current connection status and channel information
 */

import React from 'react';
import { ConnectionState, ChannelInfo } from '../index';

interface ConnectionPanelProps {
  connectionState: ConnectionState;
  channels: Map<string, ChannelInfo>;
}

export const ConnectionPanel: React.FC<ConnectionPanelProps> = ({
  connectionState,
  channels
}) => {

  const copyConnectionStateForAI = () => {
    const aiReport = {
      timestamp: new Date().toISOString(),
      reportType: "CONNECTION_STATE",
      data: {
        status: connectionState.isConnected ? "connected" : "disconnected",
        authState: connectionState.authState,
        connectionId: connectionState.connectionId,
        lastHeartbeat: connectionState.lastHeartbeat,
        reconnectAttempts: connectionState.reconnectAttempts,
        lastError: connectionState.lastError?.message || null,
        channels: Array.from(channels.values()).map(channel => ({
          name: channel.topic,
          state: channel.state,
          subscriptions: channel.subscriptionCount,
          errors: channel.errorCount,
          lastEvent: channel.lastEvent,
          id: channel.id
        }))
      },
      summary: {
        totalChannels: Array.from(channels.values()).length,
        healthyChannels: Array.from(channels.values()).filter(c => c.state === 'joined').length,
        problemChannels: Array.from(channels.values()).filter(c => c.state === 'errored').length,
        stuckChannels: Array.from(channels.values()).filter(c => c.state === 'joining').length
      }
    };

    navigator.clipboard.writeText(JSON.stringify(aiReport, null, 2));
    console.log("📋 Connection state copied for AI analysis");
  };
  const formatTimestamp = (timestamp: number | null) => {
    if (!timestamp) return 'Never';
    return new Date(timestamp).toLocaleTimeString();
  };

  const getChannelStateColor = (state: ChannelInfo['state']) => {
    switch (state) {
      case 'joined': return '#4ade80';
      case 'joining': return '#fbbf24';
      case 'leaving': return '#f97316';
      case 'closed': return '#6b7280';
      case 'errored': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getAuthStateColor = (state: ConnectionState['authState']) => {
    switch (state) {
      case 'authenticated': return '#4ade80';
      case 'unauthenticated': return '#6b7280';
      case 'refreshing': return '#fbbf24';
      default: return '#6b7280';
    }
  };

  return (
    <div style={{ padding: '16px' }}>
      {/* AI Copy Button */}
      <div style={{ marginBottom: '16px', textAlign: 'center' }}>
        <button
          onClick={copyConnectionStateForAI}
          style={{
            padding: '8px 16px',
            background: '#3b82f6',
            border: 'none',
            borderRadius: '6px',
            color: 'white',
            cursor: 'pointer',
            fontSize: '12px',
            fontWeight: 'bold',
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            margin: '0 auto'
          }}
        >
          🤖 Copy for AI Analysis
        </button>
      </div>

      {/* Connection Status */}
      <div style={{ marginBottom: '20px' }}>
        <h3 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: 'bold' }}>
          Connection Status
        </h3>
        
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px', fontSize: '12px' }}>
          <div>
            <div style={{ color: '#888', marginBottom: '4px' }}>Status</div>
            <div style={{ 
              color: connectionState.isConnected ? '#4ade80' : '#ef4444',
              fontWeight: 'bold'
            }}>
              {connectionState.isConnected ? 'Connected' : 'Disconnected'}
            </div>
          </div>
          
          <div>
            <div style={{ color: '#888', marginBottom: '4px' }}>Auth State</div>
            <div style={{ 
              color: getAuthStateColor(connectionState.authState),
              fontWeight: 'bold'
            }}>
              {connectionState.authState}
            </div>
          </div>
          
          <div>
            <div style={{ color: '#888', marginBottom: '4px' }}>Connection ID</div>
            <div style={{ 
              fontFamily: 'monospace',
              fontSize: '10px',
              color: '#ccc'
            }}>
              {connectionState.connectionId || 'None'}
            </div>
          </div>
          
          <div>
            <div style={{ color: '#888', marginBottom: '4px' }}>Last Heartbeat</div>
            <div style={{ color: '#ccc' }}>
              {formatTimestamp(connectionState.lastHeartbeat)}
            </div>
          </div>
          
          <div>
            <div style={{ color: '#888', marginBottom: '4px' }}>Reconnect Attempts</div>
            <div style={{ color: '#ccc' }}>
              {connectionState.reconnectAttempts}
            </div>
          </div>
          
          <div>
            <div style={{ color: '#888', marginBottom: '4px' }}>Active Channels</div>
            <div style={{ color: '#ccc' }}>
              {connectionState.activeChannels.length}
            </div>
          </div>
        </div>

        {/* Last Error */}
        {connectionState.lastError && (
          <div style={{ marginTop: '12px' }}>
            <div style={{ color: '#888', marginBottom: '4px', fontSize: '12px' }}>Last Error</div>
            <div style={{ 
              color: '#ef4444',
              fontSize: '11px',
              fontFamily: 'monospace',
              background: '#1a1a1a',
              padding: '8px',
              borderRadius: '4px',
              border: '1px solid #333'
            }}>
              {connectionState.lastError.message}
            </div>
          </div>
        )}
      </div>

      {/* Channels */}
      <div>
        <h3 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: 'bold' }}>
          Active Channels ({Array.from(channels.values()).length})
        </h3>
        
        {Array.from(channels.values()).length === 0 ? (
          <div style={{ color: '#888', fontSize: '12px', fontStyle: 'italic' }}>
            No active channels
          </div>
        ) : (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            {Array.from(channels.values()).map(channel => (
              <div
                key={channel.id}
                style={{
                  background: '#1a1a1a',
                  border: '1px solid #333',
                  borderRadius: '4px',
                  padding: '8px',
                  fontSize: '11px'
                }}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '4px' }}>
                  <div style={{ fontWeight: 'bold', color: '#ccc' }}>
                    {channel.topic}
                  </div>
                  <div
                    style={{
                      color: getChannelStateColor(channel.state),
                      fontSize: '10px',
                      fontWeight: 'bold'
                    }}
                  >
                    {channel.state.toUpperCase()}
                  </div>
                </div>
                
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '8px', color: '#888' }}>
                  <div>
                    <div>Subscriptions</div>
                    <div style={{ color: '#ccc' }}>{channel.subscriptionCount}</div>
                  </div>
                  <div>
                    <div>Errors</div>
                    <div style={{ color: channel.errorCount > 0 ? '#ef4444' : '#ccc' }}>
                      {channel.errorCount}
                    </div>
                  </div>
                  <div>
                    <div>Last Event</div>
                    <div style={{ color: '#ccc' }}>
                      {formatTimestamp(channel.lastEvent)}
                    </div>
                  </div>
                </div>
                
                <div style={{ 
                  marginTop: '4px',
                  fontSize: '9px',
                  color: '#666',
                  fontFamily: 'monospace'
                }}>
                  ID: {channel.id}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
