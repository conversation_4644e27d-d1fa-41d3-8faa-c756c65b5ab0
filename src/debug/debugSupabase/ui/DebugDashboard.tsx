/**
 * Debug Dashboard - Visual interface for Supabase connection monitoring
 * Development-only React component with real-time updates
 */

import React, { useState, useEffect, useCallback } from 'react';
import { createRoot } from 'react-dom/client';
import { DebugStateStore } from '../core/DebugStateStore';
import { EventLogger } from '../core/EventLogger';
import { DebugSupabaseInstance } from '../index';
import { ConnectionPanel } from './ConnectionPanel';
import { EventTimeline } from './EventTimeline';
import { ControlPanel } from './ControlPanel';
import { PerformanceMetrics } from './PerformanceMetrics';

interface DebugDashboardProps {
  store: DebugStateStore;
  logger: EventLogger;
  debugInstance: DebugSupabaseInstance;
}

export class DebugDashboard {
  private store: DebugStateStore;
  private logger: EventLogger;
  private debugInstance: DebugSupabaseInstance;
  private container: HTMLElement | null = null;
  private root: any = null;
  private isMounted = false;

  constructor(
    store: DebugStateStore,
    logger: EventLogger,
    debugInstance: DebugSupabaseInstance
  ) {
    this.store = store;
    this.logger = logger;
    this.debugInstance = debugInstance;
  }

  /**
   * Mount the dashboard to the DOM
   */
  mount(): void {
    if (this.isMounted) return;

    // Create container
    this.container = document.createElement('div');
    this.container.id = 'supabase-debug-dashboard';
    this.container.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      width: 400px;
      max-height: 80vh;
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      background: rgba(0, 0, 0, 0.95);
      border: 1px solid #333;
      border-radius: 8px;
      color: white;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
      backdrop-filter: blur(10px);
    `;

    document.body.appendChild(this.container);

    // Create React root and render
    this.root = createRoot(this.container);
    this.root.render(
      <DashboardComponent
        store={this.store}
        logger={this.logger}
        debugInstance={this.debugInstance}
      />
    );

    this.isMounted = true;
    this.logger.info('Debug Dashboard mounted');
  }

  /**
   * Unmount the dashboard
   */
  unmount(): void {
    if (!this.isMounted) return;

    if (this.root) {
      this.root.unmount();
      this.root = null;
    }

    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
      this.container = null;
    }

    this.isMounted = false;
    this.logger.info('Debug Dashboard unmounted');
  }
}

/**
 * Main Dashboard Component
 */
const DashboardComponent: React.FC<DebugDashboardProps> = ({
  store,
  logger,
  debugInstance
}) => {
  const [isMinimized, setIsMinimized] = useState(false);
  const [activeTab, setActiveTab] = useState<'connection' | 'timeline' | 'performance' | 'controls'>('connection');
  const [state, setState] = useState(store.getState());

  // Subscribe to store updates
  useEffect(() => {
    const unsubscribe = store.subscribe((newState) => {
      setState(newState);
    });

    return unsubscribe;
  }, [store]);

  const handleExportLogs = useCallback(() => {
    const logs = debugInstance.exportLogs();
    const blob = new Blob([logs], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `supabase-debug-logs-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [debugInstance]);

  const handleForceReconnect = useCallback(async () => {
    try {
      await debugInstance.forceReconnect();
    } catch (error) {
      logger.error('Force reconnect failed', error);
    }
  }, [debugInstance, logger]);

  if (isMinimized) {
    return (
      <div style={{ padding: '10px', cursor: 'pointer' }} onClick={() => setIsMinimized(false)}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <ConnectionIndicator isConnected={state.connection.isConnected} />
          <span style={{ fontSize: '12px' }}>Supabase Debug</span>
        </div>
      </div>
    );
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      {/* Header */}
      <div style={{
        padding: '12px',
        borderBottom: '1px solid #333',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <ConnectionIndicator isConnected={state.connection.isConnected} />
          <span style={{ fontWeight: 'bold', fontSize: '16px' }}>Supabase Debug</span>
        </div>
        <button
          onClick={() => setIsMinimized(true)}
          style={{
            background: 'none',
            border: 'none',
            color: '#888',
            cursor: 'pointer',
            fontSize: '18px'
          }}
        >
          −
        </button>
      </div>

      {/* Tab Navigation */}
      <div style={{
        display: 'flex',
        borderBottom: '1px solid #333',
        background: '#111'
      }}>
        {[
          { key: 'connection', label: 'Connection' },
          { key: 'timeline', label: 'Timeline' },
          { key: 'performance', label: 'Performance' },
          { key: 'controls', label: 'Controls' }
        ].map(tab => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            style={{
              flex: 1,
              padding: '8px 12px',
              background: activeTab === tab.key ? '#333' : 'transparent',
              border: 'none',
              color: activeTab === tab.key ? 'white' : '#888',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div style={{ flex: 1, overflow: 'auto', maxHeight: '400px' }}>
        {activeTab === 'connection' && (
          <ConnectionPanel connectionState={state.connection} channels={state.channels} />
        )}
        {activeTab === 'timeline' && (
          <EventTimeline timeline={state.timeline} />
        )}
        {activeTab === 'performance' && (
          <PerformanceMetrics performance={state.performance} />
        )}
        {activeTab === 'controls' && (
          <ControlPanel
            debugInstance={debugInstance}
            onExportLogs={handleExportLogs}
            onForceReconnect={handleForceReconnect}
            testingState={state.testing}
          />
        )}
      </div>

      {/* Footer */}
      <div style={{
        padding: '8px 12px',
        borderTop: '1px solid #333',
        fontSize: '11px',
        color: '#666',
        display: 'flex',
        justifyContent: 'space-between'
      }}>
        <span>Events: {state.timeline.length}</span>
        <span>Channels: {state.connection.activeChannels.length}</span>
        <span>
          {state.connection.isConnected ? (
            <span style={{ color: '#4ade80' }}>Connected</span>
          ) : (
            <span style={{ color: '#ef4444' }}>Disconnected</span>
          )}
        </span>
      </div>
    </div>
  );
};

/**
 * Connection Status Indicator
 */
const ConnectionIndicator: React.FC<{ isConnected: boolean }> = ({ isConnected }) => {
  return (
    <div
      style={{
        width: '8px',
        height: '8px',
        borderRadius: '50%',
        backgroundColor: isConnected ? '#4ade80' : '#ef4444',
        animation: isConnected ? 'none' : 'pulse 2s infinite'
      }}
    />
  );
};

// Add pulse animation styles
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }
  `;
  document.head.appendChild(style);
}

export { DashboardComponent };
