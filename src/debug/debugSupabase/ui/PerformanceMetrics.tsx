/**
 * Performance Metrics - Display performance data and statistics
 */

import React from 'react';
import { PerformanceMetrics as PerformanceData } from '../core/DebugStateStore';

interface PerformanceMetricsProps {
  performance: PerformanceData;
}

export const PerformanceMetrics: React.FC<PerformanceMetricsProps> = ({ performance }) => {
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const calculateAverage = (values: number[]) => {
    if (values.length === 0) return 0;
    return values.reduce((a, b) => a + b, 0) / values.length;
  };

  const calculateMax = (values: number[]) => {
    if (values.length === 0) return 0;
    return Math.max(...values);
  };

  const getPerformanceGrade = () => {
    const avgLatency = calculateAverage(performance.stateUpdateLatency);
    const maxLatency = calculateMax(performance.stateUpdateLatency);
    
    if (maxLatency > 200) return { grade: 'F', color: '#ef4444' };
    if (avgLatency > 100) return { grade: 'D', color: '#f97316' };
    if (avgLatency > 50) return { grade: 'C', color: '#fbbf24' };
    if (avgLatency > 20) return { grade: 'B', color: '#3b82f6' };
    return { grade: 'A', color: '#10b981' };
  };

  const performanceGrade = getPerformanceGrade();

  return (
    <div style={{ padding: '16px' }}>
      {/* Performance Grade */}
      <div style={{ marginBottom: '20px', textAlign: 'center' }}>
        <div style={{
          display: 'inline-block',
          width: '60px',
          height: '60px',
          borderRadius: '50%',
          background: performanceGrade.color,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '24px',
          fontWeight: 'bold',
          color: 'white',
          marginBottom: '8px'
        }}>
          {performanceGrade.grade}
        </div>
        <div style={{ fontSize: '12px', color: '#888' }}>
          Performance Grade
        </div>
      </div>

      {/* State Update Performance */}
      <div style={{ marginBottom: '20px' }}>
        <h3 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: 'bold' }}>
          State Update Performance
        </h3>
        
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px', fontSize: '12px' }}>
          <div>
            <div style={{ color: '#888', marginBottom: '4px' }}>Average Latency</div>
            <div style={{ 
              color: calculateAverage(performance.stateUpdateLatency) > 200 ? '#ef4444' : '#4ade80',
              fontWeight: 'bold'
            }}>
              {calculateAverage(performance.stateUpdateLatency).toFixed(2)}ms
            </div>
          </div>
          
          <div>
            <div style={{ color: '#888', marginBottom: '4px' }}>Max Latency</div>
            <div style={{ 
              color: calculateMax(performance.stateUpdateLatency) > 200 ? '#ef4444' : '#fbbf24',
              fontWeight: 'bold'
            }}>
              {calculateMax(performance.stateUpdateLatency).toFixed(2)}ms
            </div>
          </div>
          
          <div>
            <div style={{ color: '#888', marginBottom: '4px' }}>Total Updates</div>
            <div style={{ color: '#ccc' }}>
              {performance.stateUpdateLatency.length}
            </div>
          </div>
          
          <div>
            <div style={{ color: '#888', marginBottom: '4px' }}>Target</div>
            <div style={{ color: '#888', fontSize: '11px' }}>
              &lt; 200ms
            </div>
          </div>
        </div>

        {/* Latency Chart */}
        {performance.stateUpdateLatency.length > 0 && (
          <div style={{ marginTop: '12px' }}>
            <div style={{ color: '#888', marginBottom: '6px', fontSize: '11px' }}>
              Recent Latency (last {Math.min(performance.stateUpdateLatency.length, 20)} updates)
            </div>
            <div style={{ 
              height: '40px',
              background: '#1a1a1a',
              border: '1px solid #333',
              borderRadius: '4px',
              padding: '4px',
              display: 'flex',
              alignItems: 'end',
              gap: '1px'
            }}>
              {performance.stateUpdateLatency.slice(-20).map((latency, index) => {
                const height = Math.min((latency / 200) * 100, 100);
                const color = latency > 200 ? '#ef4444' : latency > 100 ? '#fbbf24' : '#4ade80';
                
                return (
                  <div
                    key={index}
                    style={{
                      flex: 1,
                      height: `${height}%`,
                      background: color,
                      minHeight: '2px',
                      borderRadius: '1px'
                    }}
                    title={`${latency.toFixed(2)}ms`}
                  />
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* Memory Usage */}
      <div style={{ marginBottom: '20px' }}>
        <h3 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: 'bold' }}>
          Memory Usage
        </h3>
        
        <div style={{ fontSize: '12px' }}>
          <div style={{ color: '#888', marginBottom: '4px' }}>Current Usage</div>
          <div style={{ color: '#ccc', fontWeight: 'bold', fontSize: '16px' }}>
            {formatBytes(performance.memoryUsage)}
          </div>
          
          {performance.memoryUsage > 0 && (
            <div style={{ 
              marginTop: '8px',
              height: '8px',
              background: '#1a1a1a',
              border: '1px solid #333',
              borderRadius: '4px',
              overflow: 'hidden'
            }}>
              <div style={{
                height: '100%',
                background: performance.memoryUsage > 50 * 1024 * 1024 ? '#ef4444' : '#4ade80',
                width: `${Math.min((performance.memoryUsage / (100 * 1024 * 1024)) * 100, 100)}%`,
                transition: 'width 0.3s ease'
              }} />
            </div>
          )}
        </div>
      </div>

      {/* Event Processing */}
      <div style={{ marginBottom: '20px' }}>
        <h3 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: 'bold' }}>
          Event Processing
        </h3>
        
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px', fontSize: '12px' }}>
          <div>
            <div style={{ color: '#888', marginBottom: '4px' }}>Average Time</div>
            <div style={{ color: '#ccc', fontWeight: 'bold' }}>
              {calculateAverage(performance.eventProcessingTime).toFixed(2)}ms
            </div>
          </div>
          
          <div>
            <div style={{ color: '#888', marginBottom: '4px' }}>Max Time</div>
            <div style={{ color: '#ccc', fontWeight: 'bold' }}>
              {calculateMax(performance.eventProcessingTime).toFixed(2)}ms
            </div>
          </div>
          
          <div>
            <div style={{ color: '#888', marginBottom: '4px' }}>Total Events</div>
            <div style={{ color: '#ccc' }}>
              {performance.eventProcessingTime.length}
            </div>
          </div>
          
          <div>
            <div style={{ color: '#888', marginBottom: '4px' }}>Events/sec</div>
            <div style={{ color: '#ccc' }}>
              {performance.eventProcessingTime.length > 0 ? 
                (1000 / calculateAverage(performance.eventProcessingTime)).toFixed(1) : 
                '0'
              }
            </div>
          </div>
        </div>
      </div>

      {/* Reconnection Performance */}
      <div>
        <h3 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: 'bold' }}>
          Reconnection Performance
        </h3>
        
        {performance.reconnectionTimes.length === 0 ? (
          <div style={{ color: '#888', fontSize: '12px', fontStyle: 'italic' }}>
            No reconnections recorded
          </div>
        ) : (
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px', fontSize: '12px' }}>
            <div>
              <div style={{ color: '#888', marginBottom: '4px' }}>Average Time</div>
              <div style={{ color: '#ccc', fontWeight: 'bold' }}>
                {calculateAverage(performance.reconnectionTimes).toFixed(0)}ms
              </div>
            </div>
            
            <div>
              <div style={{ color: '#888', marginBottom: '4px' }}>Fastest</div>
              <div style={{ color: '#4ade80', fontWeight: 'bold' }}>
                {Math.min(...performance.reconnectionTimes).toFixed(0)}ms
              </div>
            </div>
            
            <div>
              <div style={{ color: '#888', marginBottom: '4px' }}>Slowest</div>
              <div style={{ color: '#ef4444', fontWeight: 'bold' }}>
                {Math.max(...performance.reconnectionTimes).toFixed(0)}ms
              </div>
            </div>
            
            <div>
              <div style={{ color: '#888', marginBottom: '4px' }}>Total</div>
              <div style={{ color: '#ccc' }}>
                {performance.reconnectionTimes.length}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Performance Tips */}
      <div style={{ 
        marginTop: '20px',
        padding: '8px',
        background: '#1a1a1a',
        border: '1px solid #333',
        borderRadius: '4px',
        fontSize: '10px',
        color: '#888'
      }}>
        <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>Performance Tips:</div>
        <div>• State updates should be &lt; 200ms</div>
        <div>• Memory usage should stay &lt; 50MB</div>
        <div>• Event processing should be &lt; 10ms</div>
        <div>• Reconnections should be &lt; 5 seconds</div>
      </div>
    </div>
  );
};
