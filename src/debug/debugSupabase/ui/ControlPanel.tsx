/**
 * Control Panel - Manual controls and testing interface
 */

import React, { useState } from 'react';
import { DebugSupabaseInstance } from '../index';
import { TestingState } from '../core/DebugStateStore';

interface ControlPanelProps {
  debugInstance: DebugSupabaseInstance;
  onExportLogs: () => void;
  onForceReconnect: () => Promise<void>;
  testingState: TestingState;
}

export const ControlPanel: React.FC<ControlPanelProps> = ({
  debugInstance,
  onExportLogs,
  onForceReconnect,
  testingState
}) => {

  const copyFullReportForAI = () => {
    const connectionState = debugInstance.getConnectionState();
    const timeline = debugInstance.getEventTimeline();
    const recentEvents = timeline.slice(-15);

    const aiReport = {
      timestamp: new Date().toISOString(),
      reportType: "FULL_DEBUG_REPORT",
      userAction: "manual_copy_request",
      data: {
        connection: {
          status: connectionState.isConnected ? "connected" : "disconnected",
          authState: connectionState.authState,
          reconnectAttempts: connectionState.reconnectAttempts,
          lastError: connectionState.lastError?.message || null,
          activeChannels: connectionState.activeChannels.length
        },
        recentEvents: recentEvents.map(event => ({
          time: new Date(event.timestamp).toLocaleTimeString(),
          type: event.type,
          event: event.event,
          context: event.context
        })),
        patterns: {
          errorCount: timeline.filter(e => e.type === 'error').length,
          recoveryAttempts: timeline.filter(e => e.event.includes('recovery_attempt')).length,
          lastRecoveryFailed: timeline.filter(e => e.event.includes('Recovery attempt') && e.event.includes('failed')).slice(-1)[0],
          channelSubscriptionErrors: timeline.filter(e => e.event.includes('subscribe multiple times')).length
        },
        testing: {
          activeScenario: testingState.activeScenario,
          isRunning: testingState.isRunning,
          recentResults: Array.from(testingState.scenarioResults.values()).slice(-3)
        }
      }
    };

    navigator.clipboard.writeText(JSON.stringify(aiReport, null, 2));
    console.log("📋 Full debug report copied for AI analysis");
  };
  const [isReconnecting, setIsReconnecting] = useState(false);
  const [selectedScenario, setSelectedScenario] = useState<string>('');
  const [isRunningScenario, setIsRunningScenario] = useState(false);

  const scenarios = [
    { key: 'browser_refresh', label: 'Browser Refresh Test' },
    { key: 'tab_switching', label: 'Tab Switching Test' },
    { key: 'network_transition', label: 'Network Offline/Online Test' },
    { key: 'token_expiration', label: 'Token Expiration Test' },
    { key: 'multiple_channels', label: 'Multiple Channels Test' }
  ];

  const handleForceReconnect = async () => {
    setIsReconnecting(true);
    try {
      await onForceReconnect();
    } finally {
      setIsReconnecting(false);
    }
  };

  const handleRunScenario = async () => {
    if (!selectedScenario || isRunningScenario) return;

    setIsRunningScenario(true);
    try {
      await debugInstance.runScenario(selectedScenario);
    } catch (error) {
      console.error('Scenario failed:', error);
    } finally {
      setIsRunningScenario(false);
    }
  };

  const handleSimulateVisibilityChange = (state: 'visible' | 'hidden') => {
    // Simulate visibility change
    Object.defineProperty(document, 'visibilityState', {
      value: state,
      writable: true
    });
    document.dispatchEvent(new Event('visibilitychange'));
  };

  const handleSimulateNetworkChange = (online: boolean) => {
    const event = online ? 'online' : 'offline';
    window.dispatchEvent(new Event(event));
  };

  return (
    <div style={{ padding: '16px' }}>
      {/* Connection Controls */}
      <div style={{ marginBottom: '20px' }}>
        <h3 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: 'bold' }}>
          Connection Controls
        </h3>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          <button
            onClick={handleForceReconnect}
            disabled={isReconnecting}
            style={{
              padding: '8px 12px',
              background: isReconnecting ? '#333' : '#ef4444',
              border: 'none',
              borderRadius: '4px',
              color: 'white',
              cursor: isReconnecting ? 'not-allowed' : 'pointer',
              fontSize: '12px',
              fontWeight: 'bold'
            }}
          >
            {isReconnecting ? 'Reconnecting...' : 'Force Reconnect'}
          </button>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' }}>
            <button
              onClick={() => handleSimulateVisibilityChange('hidden')}
              style={{
                padding: '6px 8px',
                background: '#f59e0b',
                border: 'none',
                borderRadius: '4px',
                color: 'white',
                cursor: 'pointer',
                fontSize: '11px'
              }}
            >
              Simulate Tab Hidden
            </button>
            <button
              onClick={() => handleSimulateVisibilityChange('visible')}
              style={{
                padding: '6px 8px',
                background: '#10b981',
                border: 'none',
                borderRadius: '4px',
                color: 'white',
                cursor: 'pointer',
                fontSize: '11px'
              }}
            >
              Simulate Tab Visible
            </button>
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' }}>
            <button
              onClick={() => handleSimulateNetworkChange(false)}
              style={{
                padding: '6px 8px',
                background: '#ef4444',
                border: 'none',
                borderRadius: '4px',
                color: 'white',
                cursor: 'pointer',
                fontSize: '11px'
              }}
            >
              Simulate Offline
            </button>
            <button
              onClick={() => handleSimulateNetworkChange(true)}
              style={{
                padding: '6px 8px',
                background: '#10b981',
                border: 'none',
                borderRadius: '4px',
                color: 'white',
                cursor: 'pointer',
                fontSize: '11px'
              }}
            >
              Simulate Online
            </button>
          </div>
        </div>
      </div>

      {/* Scenario Testing */}
      <div style={{ marginBottom: '20px' }}>
        <h3 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: 'bold' }}>
          Scenario Testing
        </h3>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          <select
            value={selectedScenario}
            onChange={(e) => setSelectedScenario(e.target.value)}
            style={{
              padding: '6px 8px',
              background: '#1a1a1a',
              border: '1px solid #333',
              borderRadius: '4px',
              color: 'white',
              fontSize: '11px'
            }}
          >
            <option value="">Select a test scenario...</option>
            {scenarios.map(scenario => (
              <option key={scenario.key} value={scenario.key}>
                {scenario.label}
              </option>
            ))}
          </select>

          <button
            onClick={handleRunScenario}
            disabled={!selectedScenario || isRunningScenario || testingState.isRunning}
            style={{
              padding: '8px 12px',
              background: (!selectedScenario || isRunningScenario || testingState.isRunning) ? '#333' : '#3b82f6',
              border: 'none',
              borderRadius: '4px',
              color: 'white',
              cursor: (!selectedScenario || isRunningScenario || testingState.isRunning) ? 'not-allowed' : 'pointer',
              fontSize: '12px',
              fontWeight: 'bold'
            }}
          >
            {isRunningScenario || testingState.isRunning ? 'Running Test...' : 'Run Scenario'}
          </button>

          {testingState.activeScenario && (
            <div style={{
              padding: '8px',
              background: '#1a1a1a',
              border: '1px solid #333',
              borderRadius: '4px',
              fontSize: '11px',
              color: '#fbbf24'
            }}>
              Running: {testingState.activeScenario}
            </div>
          )}
        </div>
      </div>

      {/* Test Results */}
      {testingState.scenarioResults.size > 0 && (
        <div style={{ marginBottom: '20px' }}>
          <h3 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: 'bold' }}>
            Recent Test Results
          </h3>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '6px', maxHeight: '120px', overflowY: 'auto' }}>
            {Array.from(testingState.scenarioResults.values())
              .sort((a, b) => b.startTime - a.startTime)
              .slice(0, 5)
              .map(result => (
                <div
                  key={`${result.scenario}-${result.startTime}`}
                  style={{
                    padding: '6px 8px',
                    background: '#1a1a1a',
                    border: '1px solid #333',
                    borderRadius: '4px',
                    fontSize: '10px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}
                >
                  <div>
                    <div style={{ color: '#ccc', fontWeight: 'bold' }}>
                      {result.scenario.replace('_', ' ')}
                    </div>
                    <div style={{ color: '#888' }}>
                      {new Date(result.startTime).toLocaleTimeString()}
                      {result.endTime && ` (${result.endTime - result.startTime}ms)`}
                    </div>
                  </div>
                  <div style={{
                    color: result.success ? '#4ade80' : '#ef4444',
                    fontWeight: 'bold'
                  }}>
                    {result.success ? '✓' : '✗'}
                  </div>
                </div>
              ))}
          </div>
        </div>
      )}

      {/* Data Export */}
      <div>
        <h3 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: 'bold' }}>
          Data Export
        </h3>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          <button
            onClick={copyFullReportForAI}
            style={{
              padding: '8px 12px',
              background: '#3b82f6',
              border: 'none',
              borderRadius: '4px',
              color: 'white',
              cursor: 'pointer',
              fontSize: '12px',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              justifyContent: 'center'
            }}
          >
            🤖 Copy Full Report for AI
          </button>

          <button
            onClick={onExportLogs}
            style={{
              padding: '8px 12px',
              background: '#10b981',
              border: 'none',
              borderRadius: '4px',
              color: 'white',
              cursor: 'pointer',
              fontSize: '12px',
              fontWeight: 'bold'
            }}
          >
            Export Debug Logs (JSON)
          </button>

          <button
            onClick={() => {
              const report = debugInstance.logger.exportLogs();
              console.log('Debug Report:', report);
            }}
            style={{
              padding: '6px 8px',
              background: '#6b7280',
              border: 'none',
              borderRadius: '4px',
              color: 'white',
              cursor: 'pointer',
              fontSize: '11px'
            }}
          >
            Log Report to Console
          </button>
        </div>
      </div>
    </div>
  );
};
