/**
 * Event Timeline - Chronological display of connection events
 */

import React, { useState, useMemo } from 'react';
import { EventTimelineEntry } from '../index';

interface EventTimelineProps {
  timeline: EventTimelineEntry[];
}

export const EventTimeline: React.FC<EventTimelineProps> = ({ timeline }) => {
  const [filter, setFilter] = useState<'all' | 'connection' | 'auth' | 'visibility' | 'heartbeat' | 'channel' | 'error'>('all');
  const [showDetails, setShowDetails] = useState<string | null>(null);

  const filteredTimeline = useMemo(() => {
    const filtered = filter === 'all' 
      ? timeline 
      : timeline.filter(entry => entry.type === filter);
    
    // Sort by timestamp (newest first)
    return filtered.sort((a, b) => b.timestamp - a.timestamp);
  }, [timeline, filter]);

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const getEventTypeColor = (type: EventTimelineEntry['type']) => {
    switch (type) {
      case 'connection': return '#3b82f6';
      case 'auth': return '#10b981';
      case 'visibility': return '#f59e0b';
      case 'heartbeat': return '#8b5cf6';
      case 'channel': return '#06b6d4';
      case 'error': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getEventIcon = (type: EventTimelineEntry['type']) => {
    switch (type) {
      case 'connection': return '🔌';
      case 'auth': return '🔐';
      case 'visibility': return '👁️';
      case 'heartbeat': return '💓';
      case 'channel': return '📡';
      case 'error': return '❌';
      default: return '📝';
    }
  };

  const eventCounts = useMemo(() => {
    return timeline.reduce((acc, entry) => {
      acc[entry.type] = (acc[entry.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }, [timeline]);

  return (
    <div style={{ padding: '16px' }}>
      {/* Filter Buttons */}
      <div style={{ marginBottom: '16px' }}>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
          {[
            { key: 'all', label: `All (${timeline.length})` },
            { key: 'connection', label: `Connection (${eventCounts.connection || 0})` },
            { key: 'auth', label: `Auth (${eventCounts.auth || 0})` },
            { key: 'visibility', label: `Visibility (${eventCounts.visibility || 0})` },
            { key: 'heartbeat', label: `Heartbeat (${eventCounts.heartbeat || 0})` },
            { key: 'channel', label: `Channel (${eventCounts.channel || 0})` },
            { key: 'error', label: `Error (${eventCounts.error || 0})` }
          ].map(filterOption => (
            <button
              key={filterOption.key}
              onClick={() => setFilter(filterOption.key as any)}
              style={{
                padding: '4px 8px',
                fontSize: '10px',
                border: '1px solid #333',
                borderRadius: '4px',
                background: filter === filterOption.key ? '#333' : 'transparent',
                color: filter === filterOption.key ? 'white' : '#888',
                cursor: 'pointer'
              }}
            >
              {filterOption.label}
            </button>
          ))}
        </div>
      </div>

      {/* Timeline */}
      <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
        {filteredTimeline.length === 0 ? (
          <div style={{ color: '#888', fontSize: '12px', fontStyle: 'italic', textAlign: 'center', padding: '20px' }}>
            No events to display
          </div>
        ) : (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            {filteredTimeline.map(entry => (
              <div
                key={entry.id}
                style={{
                  background: '#1a1a1a',
                  border: '1px solid #333',
                  borderRadius: '4px',
                  padding: '8px',
                  fontSize: '11px',
                  cursor: 'pointer'
                }}
                onClick={() => setShowDetails(showDetails === entry.id ? null : entry.id)}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '4px' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                    <span>{getEventIcon(entry.type)}</span>
                    <span style={{ 
                      color: getEventTypeColor(entry.type),
                      fontWeight: 'bold',
                      fontSize: '10px'
                    }}>
                      {entry.type.toUpperCase()}
                    </span>
                    <span style={{ color: '#ccc' }}>
                      {entry.event}
                    </span>
                  </div>
                  <div style={{ color: '#666', fontSize: '10px' }}>
                    {formatTime(entry.timestamp)}
                  </div>
                </div>

                {/* Context Info */}
                <div style={{ display: 'flex', gap: '12px', fontSize: '9px', color: '#888' }}>
                  <span>
                    Visibility: <span style={{ color: '#ccc' }}>{entry.context.visibilityState}</span>
                  </span>
                  <span>
                    Connection: <span style={{ 
                      color: entry.context.connectionState === 'connected' ? '#4ade80' : '#ef4444'
                    }}>
                      {entry.context.connectionState}
                    </span>
                  </span>
                  <span>
                    Auth: <span style={{ color: '#ccc' }}>{entry.context.authState}</span>
                  </span>
                  <span>
                    Channels: <span style={{ color: '#ccc' }}>{entry.context.activeChannels}</span>
                  </span>
                </div>

                {/* Detailed Data */}
                {showDetails === entry.id && entry.data && (
                  <div style={{ 
                    marginTop: '8px',
                    padding: '8px',
                    background: '#0a0a0a',
                    border: '1px solid #222',
                    borderRadius: '4px',
                    fontSize: '9px',
                    fontFamily: 'monospace'
                  }}>
                    <div style={{ color: '#888', marginBottom: '4px' }}>Event Data:</div>
                    <pre style={{ 
                      margin: 0,
                      color: '#ccc',
                      whiteSpace: 'pre-wrap',
                      wordBreak: 'break-word'
                    }}>
                      {JSON.stringify(entry.data, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Timeline Summary */}
      {timeline.length > 0 && (
        <div style={{ 
          marginTop: '16px',
          padding: '8px',
          background: '#1a1a1a',
          border: '1px solid #333',
          borderRadius: '4px',
          fontSize: '10px',
          color: '#888'
        }}>
          <div style={{ marginBottom: '4px', fontWeight: 'bold' }}>Timeline Summary</div>
          <div>
            Total Events: {timeline.length} | 
            Showing: {filteredTimeline.length} | 
            Time Range: {formatTime(Math.min(...timeline.map(e => e.timestamp)))} - {formatTime(Math.max(...timeline.map(e => e.timestamp)))}
          </div>
        </div>
      )}
    </div>
  );
};
