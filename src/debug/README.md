# PlayBeg Database Debugger

## 🎯 Purpose
This debugging tool is designed to identify and diagnose database connectivity, RLS policy, and Realtime issues in the PlayBeg application.

## 🚀 How to Use

### 1. Setup Database Functions (One-time)
Run the SQL in `create_debug_functions.sql` in your Supabase SQL Editor to create helper functions.

### 2. Access the Debugger
Navigate to `/debug` in your browser while logged in to PlayBeg.

### 3. Run Tests
Click "Run All Tests" to execute the comprehensive test suite.

## 🔍 What It Tests

### Core Infrastructure
- **Supabase Client Health**: Verifies client initialization and configuration
- **Auth Session Check**: Validates authentication state and user session
- **WebSocket Realtime**: Tests Realtime connection and subscription capabilities

### Database Tables
- **subscription_plans**: Basic table access test
- **dj_profiles**: User profile table access
- **dj_subscriptions**: Subscription data access
- **sessions**: The problematic table - multiple test approaches
- **song_requests**: Request data access

### Sessions Table Deep Dive
- **No Filter**: Basic sessions query without user filtering
- **With User Filter**: Sessions query filtered by current user
- **Count Only**: Simple count query to test basic connectivity
- **Raw SQL**: Uses RPC function to bypass potential ORM issues
- **Multiple Attempts**: Tests consistency across multiple queries
- **Concurrent Access**: Tests if concurrent queries cause issues

### Performance & Connectivity
- **Network Timing**: Measures query response times
- **Connection Pool**: Tests multiple sequential connections
- **Concurrent Query**: Tests simultaneous queries to different tables
- **RLS Policy**: Analyzes Row Level Security policy conflicts

## 📊 Understanding Results

### Status Indicators
- ✅ **SUCCESS**: Test passed without issues
- ❌ **ERROR**: Test failed with an error
- ⏰ **TIMEOUT**: Test exceeded time limit (indicates hanging queries)
- 🔄 **RUNNING**: Test currently in progress

### Key Metrics
- **Duration**: How long each test took (timeouts indicate hanging)
- **Success Rate**: Percentage of successful attempts in multi-attempt tests
- **Error Messages**: Specific error details for failed tests

## 🎯 Common Issues & Solutions

### Sessions Query Timeout
If sessions table queries consistently timeout:
1. Check RLS policies for conflicts
2. Verify user authentication state
3. Test with RLS temporarily disabled
4. Check for database locks or performance issues

### Realtime Connection Failures
If WebSocket tests fail:
1. Check browser network restrictions
2. Verify Supabase project settings
3. Test with different network connections

### Authentication Issues
If auth tests fail:
1. Verify user is properly logged in
2. Check token expiration
3. Test login/logout cycle

## 📋 Reporting Issues

When reporting issues, include:
1. Complete JSON output from the debugger
2. Browser and OS information
3. Network environment (corporate, home, mobile)
4. Steps to reproduce the issue

## 🔧 Advanced Debugging

For deeper investigation:
1. Check browser Network tab during tests
2. Monitor Supabase logs in dashboard
3. Use browser DevTools to inspect WebSocket connections
4. Test from different devices/networks

## 🚨 Security Note

This debugger is only available to authenticated users and should only be used in development/staging environments. Remove or restrict access in production.
