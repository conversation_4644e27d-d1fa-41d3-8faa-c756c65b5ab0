-- Debug functions for PlayBeg database debugging
-- Run this in Supabase SQL Editor to create debug functions

-- Function to get session count (bypasses <PERSON><PERSON> for testing)
CREATE OR REPLACE FUNCTION get_session_count()
RETURNS INTEGER
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT COUNT(*)::INTEGER FROM public.sessions;
$$;

-- Function to test basic connectivity
CREATE OR REPLACE FUNCTION test_db_connection()
RETURNS JSON
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT json_build_object(
    'timestamp', NOW(),
    'database', current_database(),
    'user', current_user,
    'session_count', (SELECT COUNT(*) FROM public.sessions),
    'plans_count', (SELECT COUNT(*) FROM public.subscription_plans),
    'profiles_count', (SELECT COUNT(*) FROM public.dj_profiles)
  );
$$;

-- Function to test RLS policies
CREATE OR REPLACE FUNCTION test_rls_policies()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'sessions_rls_enabled', (
      SELECT row_security_active 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = 'sessions'
    ),
    'sessions_policies', (
      SELECT json_agg(policyname)
      FROM pg_policies 
      WHERE schemaname = 'public' 
      AND tablename = 'sessions'
    ),
    'current_user_id', auth.uid(),
    'is_authenticated', (auth.uid() IS NOT NULL)
  ) INTO result;
  
  RETURN result;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_session_count() TO authenticated, anon;
GRANT EXECUTE ON FUNCTION test_db_connection() TO authenticated, anon;
GRANT EXECUTE ON FUNCTION test_rls_policies() TO authenticated, anon;
