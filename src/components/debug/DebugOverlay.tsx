import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Loader2, Copy, Play, Pause, X, Minimize2, Maximize2, 
  AlertTriangle, CheckCircle, XCircle, Clock, Bug 
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface LogEntry {
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'success';
  category: string;
  message: string;
  data?: any;
}

interface OverlayState {
  isVisible: boolean;
  isMinimized: boolean;
  isRunning: boolean;
  logs: LogEntry[];
  position: { x: number; y: number };
}

export default function DebugOverlay() {
  const [state, setState] = useState<OverlayState>({
    isVisible: false,
    isMinimized: false,
    isRunning: false,
    logs: [],
    position: { x: 20, y: 20 }
  });
  
  const intervalRef = useRef<NodeJS.Timeout>();
  const dragRef = useRef<{ isDragging: boolean; offset: { x: number; y: number } }>({
    isDragging: false,
    offset: { x: 0, y: 0 }
  });

  const addLog = (level: LogEntry['level'], category: string, message: string, data?: any) => {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      category,
      message,
      data
    };

    // Save to localStorage for persistence
    const existingLogs = JSON.parse(localStorage.getItem('debugLogs') || '[]');
    const allLogs = [...existingLogs, entry].slice(-100); // Keep last 100
    localStorage.setItem('debugLogs', JSON.stringify(allLogs));

    setState(prev => ({
      ...prev,
      logs: allLogs
    }));

    console.log(`[DEBUG] [${category}] ${message}`, data || '');
  };

  const runTest = async (testName: string, testFn: () => Promise<any>) => {
    addLog('info', testName, `🚀 Starting ${testName} test...`);
    const startTime = Date.now();

    try {
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Timeout')), 3000)
      );

      addLog('info', testName, `📡 Executing ${testName} query...`);
      const result = await Promise.race([testFn(), timeoutPromise]);
      const duration = Date.now() - startTime;

      addLog('success', testName, `✅ ${duration}ms`, result);
      return { success: true, duration, result };

    } catch (error: any) {
      const duration = Date.now() - startTime;
      addLog('error', testName, `❌ ${duration}ms: ${error.message}`);
      return { success: false, duration, error: error.message };
    }
  };

  const startMonitoring = () => {
    setState(prev => ({ ...prev, isRunning: true }));
    addLog('info', 'MONITOR', 'Started continuous monitoring');

    const runTestCycle = async () => {
      if (!state.isRunning) return;

      addLog('info', 'CYCLE', '--- Starting test cycle ---');

      // Test auth state
      addLog('info', 'CYCLE', 'About to test AUTH...');
      await runTest('AUTH', async () => {
        const { data, error } = await supabase.auth.getSession();
        return {
          hasSession: !!data.session,
          userId: data.session?.user?.id?.slice(0, 8) + '...',
          error: error?.message
        };
      });

      // Test simple database query
      addLog('info', 'CYCLE', 'About to test DB...');
      await runTest('DB', async () => {
        const result = await supabase
          .from('subscription_plans')
          .select('id')
          .limit(1);

        return {
          success: !result.error,
          count: result.data?.length || 0,
          error: result.error?.message
        };
      });

      // Test sessions table
      addLog('info', 'CYCLE', 'About to test SESSIONS...');
      await runTest('SESSIONS', async () => {
        const result = await supabase
          .from('sessions')
          .select('id')
          .limit(1);

        return {
          success: !result.error,
          count: result.data?.length || 0,
          error: result.error?.message
        };
      });

      addLog('info', 'CYCLE', '--- Test cycle complete ---');
    };

    // Run immediately
    runTestCycle();
    
    // Run every 5 seconds
    intervalRef.current = setInterval(runTestCycle, 5000);
  };

  const stopMonitoring = () => {
    setState(prev => ({ ...prev, isRunning: false }));
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    addLog('info', 'MONITOR', 'Stopped monitoring');
  };

  const toggleVisibility = () => {
    setState(prev => ({ ...prev, isVisible: !prev.isVisible }));
  };

  const toggleMinimize = () => {
    setState(prev => ({ ...prev, isMinimized: !prev.isMinimized }));
  };

  const copyLogs = () => {
    const logText = state.logs.map(log => 
      `[${log.timestamp}] [${log.level}] [${log.category}] ${log.message}`
    ).join('\n');
    
    navigator.clipboard.writeText(logText);
    addLog('success', 'SYSTEM', 'Logs copied to clipboard');
  };

  const clearLogs = () => {
    localStorage.removeItem('debugLogs');
    setState(prev => ({ ...prev, logs: [] }));
  };

  // Load logs from localStorage on mount
  useEffect(() => {
    const savedLogs = JSON.parse(localStorage.getItem('debugLogs') || '[]');
    setState(prev => ({ ...prev, logs: savedLogs }));
  }, []);

  // Mouse event handlers for dragging
  const handleMouseDown = (e: React.MouseEvent) => {
    dragRef.current.isDragging = true;
    dragRef.current.offset = {
      x: e.clientX - state.position.x,
      y: e.clientY - state.position.y
    };
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (dragRef.current.isDragging) {
      setState(prev => ({
        ...prev,
        position: {
          x: e.clientX - dragRef.current.offset.x,
          y: e.clientY - dragRef.current.offset.y
        }
      }));
    }
  };

  const handleMouseUp = () => {
    dragRef.current.isDragging = false;
  };

  useEffect(() => {
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Auto-show in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      setState(prev => ({ ...prev, isVisible: true }));
    }
  }, []);

  const getLogIcon = (level: LogEntry['level']) => {
    switch (level) {
      case 'success': return <CheckCircle className="w-3 h-3 text-green-500" />;
      case 'error': return <XCircle className="w-3 h-3 text-red-500" />;
      case 'warn': return <AlertTriangle className="w-3 h-3 text-orange-500" />;
      default: return <Clock className="w-3 h-3 text-blue-500" />;
    }
  };

  // Floating debug button
  if (!state.isVisible) {
    return (
      <div 
        className="fixed bottom-4 right-4 z-50"
        style={{ display: process.env.NODE_ENV === 'development' ? 'block' : 'none' }}
      >
        <Button
          onClick={toggleVisibility}
          className="rounded-full w-12 h-12 bg-purple-600 hover:bg-purple-700 shadow-lg"
          title="Open Debug Overlay"
        >
          <Bug className="w-5 h-5" />
        </Button>
      </div>
    );
  }

  return (
    <div 
      className="fixed z-50 bg-white border border-gray-300 rounded-lg shadow-xl"
      style={{ 
        left: state.position.x, 
        top: state.position.y,
        width: state.isMinimized ? '300px' : '400px',
        maxHeight: state.isMinimized ? '60px' : '500px'
      }}
    >
      <div 
        className="flex items-center justify-between p-2 bg-gray-100 rounded-t-lg cursor-move"
        onMouseDown={handleMouseDown}
      >
        <div className="flex items-center gap-2">
          <Bug className="w-4 h-4 text-purple-600" />
          <span className="text-sm font-medium">Debug Monitor</span>
          {state.isRunning && <Loader2 className="w-3 h-3 animate-spin text-blue-500" />}
        </div>
        
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleMinimize}
            className="w-6 h-6 p-0"
          >
            {state.isMinimized ? <Maximize2 className="w-3 h-3" /> : <Minimize2 className="w-3 h-3" />}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleVisibility}
            className="w-6 h-6 p-0"
          >
            <X className="w-3 h-3" />
          </Button>
        </div>
      </div>

      {!state.isMinimized && (
        <div className="p-3">
          <div className="flex gap-2 mb-3">
            {!state.isRunning ? (
              <Button 
                onClick={startMonitoring}
                size="sm"
                className="flex items-center gap-1"
              >
                <Play className="w-3 h-3" />
                Start
              </Button>
            ) : (
              <Button 
                onClick={stopMonitoring}
                variant="destructive"
                size="sm"
                className="flex items-center gap-1"
              >
                <Pause className="w-3 h-3" />
                Stop
              </Button>
            )}
            
            <Button 
              variant="outline" 
              size="sm"
              onClick={copyLogs}
            >
              <Copy className="w-3 h-3" />
            </Button>
            
            <Button 
              variant="outline" 
              size="sm"
              onClick={clearLogs}
            >
              Clear
            </Button>
          </div>

          <div className="space-y-1 max-h-80 overflow-y-auto text-xs">
            {state.logs.length === 0 ? (
              <div className="text-center text-gray-500 py-4">
                No logs yet. Click Start to begin monitoring.
              </div>
            ) : (
              state.logs.slice(-20).map((log, index) => (
                <div key={index} className="flex items-start gap-2 p-1 border-b border-gray-100">
                  {getLogIcon(log.level)}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="font-mono text-xs text-gray-500">
                        {new Date(log.timestamp).toLocaleTimeString()}
                      </span>
                      <Badge variant="outline" className="text-xs px-1 py-0">
                        {log.category}
                      </Badge>
                    </div>
                    <div className="text-xs truncate">{log.message}</div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
}
