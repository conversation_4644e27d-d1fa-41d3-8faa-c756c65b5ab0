import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON>ader2, <PERSON><PERSON>, Play, <PERSON><PERSON><PERSON>riangle, CheckCircle, XCircle, Clock } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

interface TestResult {
  name: string;
  status: 'pending' | 'running' | 'success' | 'error' | 'timeout';
  duration?: number;
  data?: any;
  error?: string;
  details?: any;
}

interface DebugResults {
  timestamp: string;
  userAgent: string;
  userId?: string;
  tests: TestResult[];
  summary: {
    total: number;
    passed: number;
    failed: number;
    timeouts: number;
  };
}

export default function DatabaseDebugger() {
  const { user } = useAuth();
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<DebugResults | null>(null);
  const [currentTest, setCurrentTest] = useState<string>('');

  const updateTestResult = (testName: string, update: Partial<TestResult>) => {
    setResults(prev => {
      if (!prev) return prev;
      
      const updatedTests = prev.tests.map(test => 
        test.name === testName ? { ...test, ...update } : test
      );
      
      const summary = {
        total: updatedTests.length,
        passed: updatedTests.filter(t => t.status === 'success').length,
        failed: updatedTests.filter(t => t.status === 'error').length,
        timeouts: updatedTests.filter(t => t.status === 'timeout').length,
      };
      
      return {
        ...prev,
        tests: updatedTests,
        summary
      };
    });
  };

  const runTest = async (testName: string, testFn: () => Promise<any>, timeoutMs = 10000) => {
    setCurrentTest(testName);
    updateTestResult(testName, { status: 'running' });
    
    const startTime = Date.now();
    
    try {
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Test timeout')), timeoutMs)
      );
      
      const result = await Promise.race([testFn(), timeoutPromise]);
      const duration = Date.now() - startTime;
      
      updateTestResult(testName, {
        status: 'success',
        duration,
        data: result,
        details: typeof result === 'object' ? result : { result }
      });
      
    } catch (error: any) {
      const duration = Date.now() - startTime;
      const isTimeout = error.message === 'Test timeout';
      
      updateTestResult(testName, {
        status: isTimeout ? 'timeout' : 'error',
        duration,
        error: error.message,
        details: { error: error.message, stack: error.stack }
      });
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setCurrentTest('');
    
    // Initialize results
    const initialResults: DebugResults = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      userId: user?.id,
      tests: [
        { name: 'Supabase Client Health', status: 'pending' },
        { name: 'Auth Session Check', status: 'pending' },
        { name: 'WebSocket Realtime', status: 'pending' },
        { name: 'subscription_plans Table', status: 'pending' },
        { name: 'dj_profiles Table', status: 'pending' },
        { name: 'dj_subscriptions Table', status: 'pending' },
        { name: 'sessions Table (No Filter)', status: 'pending' },
        { name: 'sessions Table (With User Filter)', status: 'pending' },
        { name: 'sessions Table (Count Only)', status: 'pending' },
        { name: 'sessions Table (Raw SQL)', status: 'pending' },
        { name: 'sessions Table (Multiple Attempts)', status: 'pending' },
        { name: 'song_requests Table', status: 'pending' },
        { name: 'RLS Policy Test', status: 'pending' },
        { name: 'Network Timing Test', status: 'pending' },
        { name: 'Connection Pool Test', status: 'pending' },
        { name: 'Concurrent Query Test', status: 'pending' },
      ],
      summary: { total: 16, passed: 0, failed: 0, timeouts: 0 }
    };
    
    setResults(initialResults);

    // Test 1: Supabase Client Health
    await runTest('Supabase Client Health', async () => {
      return {
        clientExists: !!supabase,
        url: supabase.supabaseUrl,
        hasAuth: !!supabase.auth,
        hasRealtime: !!supabase.realtime,
        realtimeConnected: supabase.realtime?.isConnected()
      };
    });

    // Test 2: Auth Session
    await runTest('Auth Session Check', async () => {
      const { data, error } = await supabase.auth.getSession();
      return {
        hasSession: !!data.session,
        userId: data.session?.user?.id,
        email: data.session?.user?.email,
        expiresAt: data.session?.expires_at,
        error: error?.message
      };
    });

    // Test 3: WebSocket Realtime
    await runTest('WebSocket Realtime', async () => {
      return new Promise((resolve, reject) => {
        const channel = supabase
          .channel(`debug_test_${Date.now()}`)
          .subscribe((status) => {
            if (status === 'SUBSCRIBED') {
              channel.unsubscribe();
              resolve({ status, connected: true });
            } else if (status === 'CHANNEL_ERROR') {
              channel.unsubscribe();
              reject(new Error(`Realtime failed: ${status}`));
            }
          });
      });
    }, 15000);

    // Test 4-7: Database Tables
    const tables = [
      'subscription_plans',
      'dj_profiles', 
      'dj_subscriptions',
      'song_requests'
    ];

    for (const table of tables) {
      await runTest(`${table} Table`, async () => {
        const result = await supabase
          .from(table)
          .select('*')
          .limit(1);
        
        return {
          success: !result.error,
          count: result.data?.length || 0,
          error: result.error?.message,
          data: result.data?.[0] || null
        };
      });
    }

    // Test 8: Sessions Table (No Filter)
    await runTest('sessions Table (No Filter)', async () => {
      const result = await supabase
        .from('sessions')
        .select('*')
        .limit(1);
      
      return {
        success: !result.error,
        count: result.data?.length || 0,
        error: result.error?.message,
        data: result.data?.[0] || null
      };
    });

    // Test 9: Sessions Table (With User Filter)
    if (user?.id) {
      await runTest('sessions Table (With User Filter)', async () => {
        const result = await supabase
          .from('sessions')
          .select('*')
          .eq('dj_id', user.id)
          .limit(1);
        
        return {
          success: !result.error,
          count: result.data?.length || 0,
          error: result.error?.message,
          data: result.data?.[0] || null
        };
      });
    }

    // Test 10: Sessions Count
    await runTest('sessions Table (Count Only)', async () => {
      const result = await supabase
        .from('sessions')
        .select('*', { count: 'exact', head: true });
      
      return {
        success: !result.error,
        totalCount: result.count,
        error: result.error?.message
      };
    });

    // Test 11: RLS Policy Test
    await runTest('RLS Policy Test', async () => {
      // Test if we can access sessions without auth context
      const { data: sessionData } = await supabase.auth.getSession();
      const isAuthenticated = !!sessionData.session;
      
      // Try to access sessions as current user
      const result = await supabase
        .from('sessions')
        .select('id, dj_id, active')
        .limit(5);
      
      return {
        isAuthenticated,
        canAccessSessions: !result.error,
        sessionCount: result.data?.length || 0,
        error: result.error?.message,
        rlsBlocking: result.error?.message?.includes('policy') || false
      };
    });

    // Test 11: Sessions Raw SQL Test
    await runTest('sessions Table (Raw SQL)', async () => {
      const result = await supabase.rpc('get_session_count');
      return {
        success: !result.error,
        count: result.data,
        error: result.error?.message,
        usedRPC: true
      };
    });

    // Test 12: Sessions Multiple Attempts
    await runTest('sessions Table (Multiple Attempts)', async () => {
      const attempts = [];

      for (let i = 0; i < 5; i++) {
        const start = Date.now();
        try {
          const result = await supabase
            .from('sessions')
            .select('id')
            .limit(1);

          const duration = Date.now() - start;
          attempts.push({
            attempt: i + 1,
            success: !result.error,
            duration,
            error: result.error?.message
          });
        } catch (error: any) {
          const duration = Date.now() - start;
          attempts.push({
            attempt: i + 1,
            success: false,
            duration,
            error: error.message
          });
        }

        // Small delay between attempts
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      return {
        attempts,
        successRate: attempts.filter(a => a.success).length / attempts.length,
        averageDuration: attempts.reduce((sum, a) => sum + a.duration, 0) / attempts.length,
        consistentFailure: attempts.every(a => !a.success)
      };
    });

    // Test 13: Network Timing
    await runTest('Network Timing Test', async () => {
      const tests = [];

      // Test multiple quick queries
      for (let i = 0; i < 3; i++) {
        const start = Date.now();
        const result = await supabase
          .from('subscription_plans')
          .select('id')
          .limit(1);
        const duration = Date.now() - start;

        tests.push({
          attempt: i + 1,
          duration,
          success: !result.error,
          error: result.error?.message
        });
      }

      return {
        tests,
        averageDuration: tests.reduce((sum, t) => sum + t.duration, 0) / tests.length,
        allSuccessful: tests.every(t => t.success)
      };
    });

    // Test 14: Connection Pool Test
    await runTest('Connection Pool Test', async () => {
      // Test if multiple clients cause issues
      const results = [];

      for (let i = 0; i < 3; i++) {
        const start = Date.now();
        const result = await supabase
          .from('subscription_plans')
          .select('id')
          .limit(1);
        const duration = Date.now() - start;

        results.push({
          connection: i + 1,
          duration,
          success: !result.error
        });
      }

      return {
        results,
        allSuccessful: results.every(r => r.success),
        maxDuration: Math.max(...results.map(r => r.duration)),
        minDuration: Math.min(...results.map(r => r.duration))
      };
    });

    // Test 15: Concurrent Query Test
    await runTest('Concurrent Query Test', async () => {
      const start = Date.now();

      // Run multiple queries concurrently
      const promises = [
        supabase.from('subscription_plans').select('id').limit(1),
        supabase.from('dj_profiles').select('id').limit(1),
        supabase.from('sessions').select('id').limit(1)
      ];

      const results = await Promise.allSettled(promises);
      const duration = Date.now() - start;

      return {
        duration,
        results: results.map((result, index) => ({
          table: ['subscription_plans', 'dj_profiles', 'sessions'][index],
          status: result.status,
          success: result.status === 'fulfilled' && !(result.value as any).error,
          error: result.status === 'rejected' ? result.reason.message : (result.value as any).error?.message
        })),
        allSuccessful: results.every(r => r.status === 'fulfilled' && !(r.value as any).error)
      };
    });

    setIsRunning(false);
    setCurrentTest('');
  };

  const copyResults = () => {
    if (results) {
      navigator.clipboard.writeText(JSON.stringify(results, null, 2));
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'running': return <Loader2 className="w-4 h-4 animate-spin text-blue-500" />;
      case 'success': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error': return <XCircle className="w-4 h-4 text-red-500" />;
      case 'timeout': return <Clock className="w-4 h-4 text-orange-500" />;
      default: return <div className="w-4 h-4 rounded-full bg-gray-300" />;
    }
  };

  const getStatusBadge = (status: TestResult['status']) => {
    const variants = {
      pending: 'secondary',
      running: 'default',
      success: 'default',
      error: 'destructive',
      timeout: 'secondary'
    } as const;
    
    return (
      <Badge variant={variants[status]} className="ml-2">
        {status.toUpperCase()}
      </Badge>
    );
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-orange-500" />
            PlayBeg Database Debugger
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Comprehensive debugging tool to identify database and realtime issues
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 mb-4">
            <Button 
              onClick={runAllTests} 
              disabled={isRunning}
              className="flex items-center gap-2"
            >
              {isRunning ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Play className="w-4 h-4" />
              )}
              {isRunning ? 'Running Tests...' : 'Run All Tests'}
            </Button>
            
            {results && (
              <Button 
                variant="outline" 
                onClick={copyResults}
                className="flex items-center gap-2"
              >
                <Copy className="w-4 h-4" />
                Copy Results
              </Button>
            )}
          </div>

          {isRunning && currentTest && (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2">
                <Loader2 className="w-4 h-4 animate-spin text-blue-500" />
                <span className="text-sm font-medium">Running: {currentTest}</span>
              </div>
            </div>
          )}

          {results && (
            <div className="space-y-4">
              <div className="grid grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold">{results.summary.total}</div>
                  <div className="text-sm text-muted-foreground">Total Tests</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{results.summary.passed}</div>
                  <div className="text-sm text-muted-foreground">Passed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{results.summary.failed}</div>
                  <div className="text-sm text-muted-foreground">Failed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{results.summary.timeouts}</div>
                  <div className="text-sm text-muted-foreground">Timeouts</div>
                </div>
              </div>

              <div className="space-y-2">
                {results.tests.map((test, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(test.status)}
                      <span className="font-medium">{test.name}</span>
                      {getStatusBadge(test.status)}
                    </div>
                    <div className="text-right text-sm text-muted-foreground">
                      {test.duration && `${test.duration}ms`}
                      {test.error && (
                        <div className="text-red-600 max-w-xs truncate">
                          {test.error}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
