import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, Co<PERSON>, Play, AlertTriangle, CheckCircle, XCircle, Clock, Pause } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface LogEntry {
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'success';
  category: string;
  message: string;
  data?: any;
}

interface TestState {
  isRunning: boolean;
  logs: LogEntry[];
  currentTest: string;
  startTime: number;
}

export default function RealtimeDebugger() {
  const [state, setState] = useState<TestState>({
    isRunning: false,
    logs: [],
    currentTest: '',
    startTime: 0
  });
  
  const intervalRef = useRef<NodeJS.Timeout>();
  const testCountRef = useRef(0);

  const addLog = (level: LogEntry['level'], category: string, message: string, data?: any) => {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      category,
      message,
      data
    };
    
    setState(prev => ({
      ...prev,
      logs: [...prev.logs, entry]
    }));
    
    console.log(`[${category}] ${message}`, data || '');
  };

  const runSingleTest = async (testName: string, testFn: () => Promise<any>) => {
    const testId = ++testCountRef.current;
    addLog('info', 'TEST', `Starting ${testName} (Test #${testId})`);
    
    const startTime = Date.now();
    
    try {
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Test timeout after 5s')), 5000)
      );
      
      const result = await Promise.race([testFn(), timeoutPromise]);
      const duration = Date.now() - startTime;
      
      addLog('success', 'TEST', `${testName} completed in ${duration}ms`, result);
      return { success: true, duration, result };
      
    } catch (error: any) {
      const duration = Date.now() - startTime;
      addLog('error', 'TEST', `${testName} failed after ${duration}ms: ${error.message}`);
      return { success: false, duration, error: error.message };
    }
  };

  const runContinuousTests = async () => {
    setState(prev => ({ ...prev, isRunning: true, startTime: Date.now() }));
    addLog('info', 'SYSTEM', 'Starting continuous debugging session');

    // Test 1: Auth State Monitoring
    addLog('info', 'AUTH', 'Monitoring auth state...');
    const authTest = async () => {
      const { data, error } = await supabase.auth.getSession();
      return {
        hasSession: !!data.session,
        userId: data.session?.user?.id,
        error: error?.message,
        tokenExpiry: data.session?.expires_at
      };
    };

    // Test 2: Simple Database Query
    const dbTest = async () => {
      const result = await supabase
        .from('subscription_plans')
        .select('id')
        .limit(1);
      
      return {
        success: !result.error,
        count: result.data?.length || 0,
        error: result.error?.message,
        status: result.status
      };
    };

    // Test 3: Sessions Query
    const sessionsTest = async () => {
      const result = await supabase
        .from('sessions')
        .select('id')
        .limit(1);
      
      return {
        success: !result.error,
        count: result.data?.length || 0,
        error: result.error?.message,
        status: result.status
      };
    };

    // Test 4: Network Connectivity
    const networkTest = async () => {
      const start = Date.now();
      const response = await fetch(`${supabase.supabaseUrl}/rest/v1/`, {
        method: 'HEAD',
        headers: {
          'apikey': supabase.supabaseKey,
          'Authorization': `Bearer ${supabase.supabaseKey}`
        }
      });
      const duration = Date.now() - start;
      
      return {
        status: response.status,
        ok: response.ok,
        duration,
        headers: Object.fromEntries(response.headers.entries())
      };
    };

    // Run tests every 2 seconds
    const runTestCycle = async () => {
      if (!state.isRunning) return;
      
      addLog('info', 'CYCLE', '--- Starting test cycle ---');
      
      // Run all tests in parallel to see which ones hang
      const testPromises = [
        runSingleTest('Auth Check', authTest),
        runSingleTest('DB Simple', dbTest),
        runSingleTest('Sessions Query', sessionsTest),
        runSingleTest('Network Test', networkTest)
      ];
      
      const results = await Promise.allSettled(testPromises);
      
      results.forEach((result, index) => {
        const testNames = ['Auth Check', 'DB Simple', 'Sessions Query', 'Network Test'];
        if (result.status === 'rejected') {
          addLog('error', 'CYCLE', `${testNames[index]} promise rejected: ${result.reason}`);
        }
      });
      
      addLog('info', 'CYCLE', '--- Test cycle complete ---');
    };

    // Initial test
    await runTestCycle();
    
    // Set up interval for continuous testing
    intervalRef.current = setInterval(runTestCycle, 3000);
  };

  const stopTests = () => {
    setState(prev => ({ ...prev, isRunning: false }));
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    addLog('info', 'SYSTEM', 'Debugging session stopped');
  };

  const clearLogs = () => {
    setState(prev => ({ ...prev, logs: [] }));
    testCountRef.current = 0;
  };

  const copyLogs = () => {
    const logText = state.logs.map(log => 
      `[${log.timestamp}] [${log.level.toUpperCase()}] [${log.category}] ${log.message}${log.data ? '\n' + JSON.stringify(log.data, null, 2) : ''}`
    ).join('\n');
    
    navigator.clipboard.writeText(logText);
    addLog('success', 'SYSTEM', 'Logs copied to clipboard');
  };

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const getLogIcon = (level: LogEntry['level']) => {
    switch (level) {
      case 'success': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error': return <XCircle className="w-4 h-4 text-red-500" />;
      case 'warn': return <AlertTriangle className="w-4 h-4 text-orange-500" />;
      default: return <Clock className="w-4 h-4 text-blue-500" />;
    }
  };

  const getLogBadge = (level: LogEntry['level']) => {
    const variants = {
      info: 'secondary',
      warn: 'secondary',
      success: 'default',
      error: 'destructive'
    } as const;
    
    return (
      <Badge variant={variants[level]} className="text-xs">
        {level.toUpperCase()}
      </Badge>
    );
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-orange-500" />
            PlayBeg Realtime Debugger
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Continuous monitoring of auth, database, and network connectivity during page lifecycle
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 mb-4">
            {!state.isRunning ? (
              <Button 
                onClick={runContinuousTests}
                className="flex items-center gap-2"
              >
                <Play className="w-4 h-4" />
                Start Continuous Testing
              </Button>
            ) : (
              <Button 
                onClick={stopTests}
                variant="destructive"
                className="flex items-center gap-2"
              >
                <Pause className="w-4 h-4" />
                Stop Testing
              </Button>
            )}
            
            <Button 
              variant="outline" 
              onClick={clearLogs}
              disabled={state.isRunning}
            >
              Clear Logs
            </Button>
            
            <Button 
              variant="outline" 
              onClick={copyLogs}
              className="flex items-center gap-2"
            >
              <Copy className="w-4 h-4" />
              Copy Logs
            </Button>
          </div>

          {state.isRunning && (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2">
                <Loader2 className="w-4 h-4 animate-spin text-blue-500" />
                <span className="text-sm font-medium">
                  Continuous testing active - {Math.floor((Date.now() - state.startTime) / 1000)}s elapsed
                </span>
              </div>
            </div>
          )}

          <div className="space-y-2 max-h-96 overflow-y-auto">
            {state.logs.length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                No logs yet. Start testing to see real-time debugging information.
              </div>
            ) : (
              state.logs.slice(-50).map((log, index) => (
                <div key={index} className="flex items-start gap-3 p-2 border rounded text-sm">
                  {getLogIcon(log.level)}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-mono text-xs text-muted-foreground">
                        {new Date(log.timestamp).toLocaleTimeString()}
                      </span>
                      <Badge variant="outline" className="text-xs">
                        {log.category}
                      </Badge>
                      {getLogBadge(log.level)}
                    </div>
                    <div className="text-sm">{log.message}</div>
                    {log.data && (
                      <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                        {JSON.stringify(log.data, null, 2)}
                      </pre>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
