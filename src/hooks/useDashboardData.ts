import { useState, useCallback, useRef, useEffect, useMemo } from "react";
import { supabase } from '@/integrations/supabase/client';
import { SessionWithSettings } from '@/types/session';

interface Subscription {
  id: string;
  dj_id: string;
  plan_id: string | null;
  status: string;
  stripe_customer_id: string | null;
  stripe_subscription_id: string | null;
  current_period_start: string | null;
  current_period_end: string | null;
  cancel_at_period_end: boolean | null;
  created_at: string;
  updated_at: string;
}

export function useDashboardData(userId?: string) {
  const [sessions, setSessions] = useState<SessionWithSettings[]>([]);
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const abortControllerRef = useRef<AbortController>();
  const subscriptionsRef = useRef<any[]>([]);

  // Only log once when userId changes
  const hasLoggedRef = useRef<string | undefined>();
  if (hasLoggedRef.current !== userId && process.env.NODE_ENV === 'development') {
    console.log('✅ DASHBOARD_DATA: Hook initialized with userId:', userId);
    hasLoggedRef.current = userId;
  }

  const fetchSessionsOnly = useCallback(async () => {
    if (!userId) return;

    // Cancel any in-progress fetches
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller for this fetch
    abortControllerRef.current = new AbortController();

    setLoading(true);
    setError(null);

    try {
      const sessionsResult = await supabase
        .from('sessions')
        .select('*')
        .eq('dj_id', userId)
        .order('created_at', { ascending: false });

      // Handle any errors
      if (sessionsResult.error) throw sessionsResult.error;

      // Update sessions state only
      setSessions(sessionsResult.data || []);
    } catch (err) {
      console.error('Failed to fetch sessions:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch sessions'));
    } finally {
      setLoading(false);
      abortControllerRef.current = undefined;
    }
  }, [userId]);

  const fetchAllData = useCallback(async () => {
    if (!userId) return;

    console.log('✅ DASHBOARD_DATA: fetchAllData called for userId:', userId);

    // Cancel any in-progress fetches
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller for this fetch
    abortControllerRef.current = new AbortController();

    setLoading(true);
    setError(null);

    try {
      console.log('🚀 DASHBOARD_DATA: Starting database queries for userId:', userId);

      // Fetch sessions and subscriptions individually to debug which hangs
      console.log('📡 DASHBOARD_DATA: Starting sessions query...');

      // Add timeout to see if query hangs forever
      // Test without dj_id filter to see if that's the problem
      const sessionsPromise = supabase
        .from('sessions')
        .select('*')
        .order('created_at', { ascending: false });

      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Sessions query timeout after 10s')), 10000)
      );

      const sessionsResult = await Promise.race([sessionsPromise, timeoutPromise]);
      console.log('✅ DASHBOARD_DATA: Sessions query completed');

      console.log('📡 DASHBOARD_DATA: Starting subscriptions query...');
      const subscriptionsResult = await supabase
        .from('dj_subscriptions')
        .select('*')
        .eq('dj_id', userId);

      console.log('✅ DASHBOARD_DATA: Subscriptions query completed');

      // Debug logging
      console.log('📊 DASHBOARD_DATA: Sessions query result:', {
        error: sessionsResult.error,
        count: sessionsResult.data?.length || 0
      });
      console.log('📊 DASHBOARD_DATA: Subscriptions query result:', {
        error: subscriptionsResult.error,
        count: subscriptionsResult.data?.length || 0
      });

      // Handle any errors
      if (sessionsResult.error) throw sessionsResult.error;
      if (subscriptionsResult.error) throw subscriptionsResult.error;

      // Update state with new data
      setSessions(sessionsResult.data || []);
      setSubscriptions(subscriptionsResult.data || []);

      console.log('✅ DASHBOARD_DATA: State updated successfully');
    } catch (err) {
      console.error('❌ DASHBOARD_DATA: Failed to fetch dashboard data:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch data'));
    } finally {
      console.log('🏁 DASHBOARD_DATA: Setting loading to false');
      setLoading(false);
      abortControllerRef.current = undefined;
    }
  }, [userId]);

  // Fetch data and set up subscriptions when userId changes
  useEffect(() => {
    if (!userId) return;

    console.log('✅ DASHBOARD_DATA: Setting up data fetch and subscriptions for userId:', userId);

    // Fetch initial data
    fetchAllData();

    // Set up realtime subscriptions
    const sessionChangesChannel = supabase
      .channel(`dashboard-session-changes-${userId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'sessions',
        filter: `dj_id=eq.${userId}`
      }, (payload) => {
        console.log('✅ Session change detected, refreshing data');
        fetchAllData();
      })
      .subscribe();

    const subscriptionChangesChannel = supabase
      .channel(`dashboard-subscription-changes-${userId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'dj_subscriptions',
        filter: `dj_id=eq.${userId}`
      }, (payload) => {
        console.log('✅ Subscription change detected, refreshing data');
        fetchAllData();
      })
      .subscribe();

    // Store subscriptions in ref for cleanup
    subscriptionsRef.current = [sessionChangesChannel, subscriptionChangesChannel];

    return () => {
      console.log('✅ DASHBOARD_DATA: Cleaning up subscriptions for userId:', userId);
      subscriptionsRef.current.forEach(channel => {
        supabase.removeChannel(channel);
      });
      subscriptionsRef.current = [];
    };
  }, [userId, fetchAllData]); // Include fetchAllData since it's stable with useCallback

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    sessions,
    subscriptions,
    loading,
    error,
    fetchAllData,
    fetchSessionsOnly,
  };
}
