import { useEffect, useState, useRef, useMemo, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useMusicStore } from '@/stores/musicStore';
import DashboardTabs from '@/components/dashboard/DashboardTabs';
import { LastLoginInfo } from '@/components/dashboard/LastLoginInfo';
import ConnectionStatus from '@/components/apple-music/ConnectionStatus';
import { useDashboardData } from '@/hooks/useDashboardData';
import { Loader2 } from 'lucide-react';
import { useAppleMusicAuth } from '@/hooks/useAppleMusicAuth';
import { supabase } from '@/integrations/supabase/client';
import { DJProfile } from '@/lib/types';

let renderCount = 0;
let lastUserId: string | undefined = undefined;

export default function Dashboard() {
  const navigate = useNavigate();
  const { user, loading: authLoading } = useAuth();

  // Reset render count when user changes (new login)
  if (user?.id !== lastUserId) {
    renderCount = 0;
    lastUserId = user?.id;
  }

  renderCount++;
  console.log(`✅ DASHBOARD: Render #${renderCount} ${renderCount > 2 ? '⚠️ EXCESSIVE RENDERS!' : ''} for user: ${user?.id || 'none'}`);

  const { connectionState, isAuthorized, checkConnection } = useMusicStore();
  const { sessions: rawSessions, subscriptions, loading: dataLoading, error, fetchAllData } = useDashboardData(user?.id);

  // Memoize sessions array to prevent unnecessary re-renders when reference changes but content is the same
  const sessions = useMemo(() => rawSessions, [rawSessions]);
  const [searchParams, setSearchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState(searchParams.get('tab') || 'sessions');
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(searchParams.get('session'));
  const [viewMode, setViewMode] = useState<string | null>(searchParams.get('view'));
  const [userProfile, setUserProfile] = useState<DJProfile | null>(null);

  // Memoize callback functions to prevent unnecessary re-renders
  const handleTabChange = useCallback((tab: string) => {
    setActiveTab(tab);
    const newParams = new URLSearchParams(searchParams);
    newParams.set('tab', tab);
    if (tab !== 'sessions' && selectedSessionId) {
      newParams.delete('session');
      setSelectedSessionId(null);
    }
    setSearchParams(newParams);
  }, [searchParams, setSearchParams, selectedSessionId]);

  const handleSessionChange = useCallback((sessionId: string | null) => {
    setSelectedSessionId(sessionId);
    const newParams = new URLSearchParams(searchParams);
    if (sessionId) {
      newParams.set('session', sessionId);
    } else {
      newParams.delete('session');
      newParams.delete('view'); // Clear view when going back to sessions list
      setViewMode(null);
    }
    setSearchParams(newParams);
  }, [searchParams, setSearchParams]);

  const handleViewModeChange = useCallback((view: string | null) => {
    setViewMode(view);
    const newParams = new URLSearchParams(searchParams);
    if (view) {
      newParams.set('view', view);
    } else {
      newParams.delete('view');
    }
    setSearchParams(newParams);
  }, [searchParams, setSearchParams]);
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);
  const FETCH_COOLDOWN = 30 * 1000; // 30 seconds cooldown for fetchAllData
  const CHECK_COOLDOWN = 5000; // 5 seconds cooldown for connection checks
  const { syncConnectionState } = useAppleMusicAuth();
  const hasInitialized = useRef(false);

  // Component mount/unmount tracking
  useEffect(() => {
    console.log('✅ DASHBOARD: Component mounted (should only see once)');
    return () => {
      console.log('⚠️ DASHBOARD: Component unmounting (should not see during normal use)');
    };
  }, []);

  useEffect(() => {
    if (!authLoading && !user) {
      navigate('/login');
    }
  }, [user, authLoading, navigate]);

  // Initialize Apple Music connection on mount - only once
  useEffect(() => {
    if (user && !authLoading && !hasInitialized.current && syncConnectionState) {
      hasInitialized.current = true;
      // Wrap in try/catch to prevent unhandled promise rejections
      try {
        // Ensure we're calling a function that returns a promise
        const syncPromise = syncConnectionState();

        // Only call .catch if it's a proper Promise
        if (syncPromise && typeof syncPromise.catch === 'function') {
          syncPromise.catch(error => {
            console.error('Error syncing Apple Music connection state:', error);
            // Continue with the app even if Apple Music fails to initialize
          });
        }
      } catch (error) {
        console.error('Exception during Apple Music connection sync:', error);
        // Continue with the app even if Apple Music fails to initialize
      }
    }
  }, [user, authLoading, syncConnectionState]);

  // Fetch dashboard data when component mounts or user changes
  useEffect(() => {
    if (user && !authLoading) {
      console.log('✅ DASHBOARD: Fetching data for user (no fetchAllData dependency loop)');
      fetchAllData();

      // Set initial fetch time to prevent immediate refresh on visibility change
      setLastFetchTime(Date.now());

      // Fetch user profile to get display name
      const fetchUserProfile = async () => {
        try {
          const { data, error } = await supabase
            .from('dj_profiles')
            .select('*')
            .eq('id', user.id)
            .maybeSingle();

          if (error) throw error;

          if (data) {
            setUserProfile(data as DJProfile);
          }
        } catch (error) {
          console.error('Error fetching user profile:', error);
        }
      };

      fetchUserProfile();
    }

    // No cleanup needed for this effect
  }, [user?.id, authLoading]); // Removed fetchAllData to prevent infinite loops

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        const now = Date.now();

        // Always check connection with shorter cooldown
        if (now - lastFetchTime >= CHECK_COOLDOWN) {
          checkConnection();
          // Only sync connection state if we haven't initialized yet
          if (!hasInitialized.current) {
            syncConnectionState();
            hasInitialized.current = true;
          }
        }

        // Only refresh dashboard data after longer cooldown to prevent
        // unnecessary remounting when opening display windows
        if (now - lastFetchTime >= FETCH_COOLDOWN) {
          console.log('🔍 DASHBOARD: Refreshing data after', (now - lastFetchTime) / 1000, 'seconds away');
          fetchAllData();
          setLastFetchTime(now);
        } else {
          console.log('🔍 DASHBOARD: Skipping data refresh, only', (now - lastFetchTime) / 1000, 'seconds since last fetch');
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [lastFetchTime]); // Removed function dependencies to prevent render loops

  // Use state for isDashboardReady to prevent it from flipping back to false
  const [isDashboardReady, setIsDashboardReady] = useState(false);

  useEffect(() => {
    // Dashboard is ready when auth is loaded, data is loaded, user exists, and sessions array is defined (even if empty)
    if (!authLoading && !dataLoading && user && Array.isArray(sessions)) {
      if (!isDashboardReady) {
        console.log('✅ DASHBOARD: Setting isDashboardReady to true (one-way transition)');
        console.log('📊 DASHBOARD: Sessions array loaded with', sessions.length, 'sessions');
        setIsDashboardReady(true);
      }
    }
  }, [authLoading, dataLoading, user, sessions, isDashboardReady]);

  if (!isDashboardReady) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-primary mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">Loading Dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-red-500">Error loading dashboard data: {error.message}</div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="container mx-auto px-0 md:px-4 py-0 md:py-8">
      {/* Mobile Header - Only visible on mobile */}
      <div className="flex items-center gap-4 px-4 py-3 md:hidden">
        <div className="w-10 h-10 bg-purple-600 text-white text-xl font-bold flex items-center justify-center rounded-full overflow-hidden flex-shrink-0">
          {userProfile?.profile_image_url ? (
            <img
              src={userProfile.profile_image_url}
              alt={userProfile?.display_name || "DJ"}
              className="w-full h-full object-cover"
            />
          ) : (
            <span>{userProfile?.display_name?.charAt(0) || "D"}</span>
          )}
        </div>
        <div className="flex flex-col">
          <h1 className="text-xl font-bold">DJ Dashboard</h1>
          <div className="flex flex-col">
            <h2 className="text-sm font-medium">
              Welcome{userProfile?.display_name ? `, ${userProfile.display_name}` : ""}
            </h2>
            <p className="text-xs text-muted-foreground">Manage your sessions and playlists.</p>
            <LastLoginInfo lastLoginAt={userProfile?.last_login_at} className="mt-1" />
          </div>
        </div>
      </div>

      {/* Welcome Section - Only visible on desktop */}
      <div className="hidden md:block px-4 mb-8">
        <h1 className="text-3xl font-bold mb-2">
          Welcome{userProfile?.display_name ? `, ${userProfile.display_name}` : ""}
        </h1>
        <p className="text-muted-foreground">Manage your sessions and playlists.</p>
        <LastLoginInfo lastLoginAt={userProfile?.last_login_at} className="mt-2" />
      </div>

      <div className="px-4 mb-3 md:mb-6">
        <ConnectionStatus />
      </div>

      <DashboardTabs
        activeTab={activeTab}
        onTabChange={handleTabChange}
        isAppleMusicConnected={isAuthorized}
        selectedSessionId={selectedSessionId}
        onSelectSession={handleSessionChange}
        viewMode={viewMode}
        onViewModeChange={handleViewModeChange}
        sessions={sessions}
        loading={dataLoading}
        onSessionCreated={fetchAllData}
        onSessionDeleted={fetchAllData}
      />
    </div>
  );
}
