
import React from 'react'
import ReactDOM from 'react-dom/client'
import './index.css'
import './styles/email-protection.css'
import './styles/partner-form.css'
import { Toaster } from "@/components/ui/toaster"
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import App from './App'
import './utils/pageLoadDebugger' // Auto-start page load debugging

// Initialize Supabase Debug Console in development
if (process.env.NODE_ENV === 'development') {
  console.log('🚀 Debug console temporarily disabled - testing app without debug console...');

  // TEMPORARILY DISABLED FOR TESTING
  /*
  // Load full debug console with correct import path
  import('./debug/debugSupabase').then(({ debugSupabase }) => {
    import('./integrations/supabase/client').then(({ supabase }) => {
      debugSupabase.init({
        supabaseClient: supabase,
        enableDashboard: true,
        enableTesting: true,
        enableLogging: true,
        logLevel: 'debug'
      }).catch((error) => {
        console.error('❌ Debug console failed:', error);
        // Fallback to simple debug if full version fails
        import('./debug/debugSupabase/simple-debug').then(({ initSimpleDebug }) => {
          initSimpleDebug({ supabaseClient: supabase, enableLogging: true });
        });
      });
    }).catch((error) => {
      console.error('❌ Failed to import supabase:', error);
    });
  }).catch((error) => {
    console.error('❌ Failed to import debug console:', error);
  });
  */
}

// Initialize React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false, // ✅ This is already disabled
    },
  },
});

// Window event logging removed - issue resolved

ReactDOM.createRoot(document.getElementById('root')!).render(
  // <React.StrictMode> // TEMPORARILY DISABLED FOR WEBSOCKET TESTING
    <QueryClientProvider client={queryClient}>
      <App />
      <Toaster />
    </QueryClientProvider>
  // </React.StrictMode>
)
