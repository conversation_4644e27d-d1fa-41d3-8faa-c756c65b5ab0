// Simple console-based debugger that actually works
import { supabase } from '@/integrations/supabase/client';

interface DebugResult {
  test: string;
  status: 'success' | 'error' | 'timeout';
  duration: number;
  data?: any;
  error?: string;
}

class SimpleDebugger {
  private results: DebugResult[] = [];
  private startTime: number = 0;

  constructor() {
    console.log('🔧 Simple Debugger initialized');
  }

  private log(message: string, data?: any) {
    const elapsed = Date.now() - this.startTime;
    console.log(`[${elapsed}ms] ${message}`, data || '');
  }

  private async runTest(testName: string, testFn: () => Promise<any>, timeoutMs = 5000): Promise<DebugResult> {
    this.log(`🚀 Starting ${testName}...`);
    const start = Date.now();

    try {
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Test timeout')), timeoutMs)
      );

      const result = await Promise.race([testFn(), timeoutPromise]);
      const duration = Date.now() - start;

      const testResult: DebugResult = {
        test: testName,
        status: 'success',
        duration,
        data: result
      };

      this.log(`✅ ${testName} completed in ${duration}ms`, result);
      this.results.push(testResult);
      return testResult;

    } catch (error: any) {
      const duration = Date.now() - start;
      const isTimeout = error.message === 'Test timeout';

      const testResult: DebugResult = {
        test: testName,
        status: isTimeout ? 'timeout' : 'error',
        duration,
        error: error.message
      };

      this.log(`❌ ${testName} ${isTimeout ? 'timeout' : 'failed'} after ${duration}ms: ${error.message}`);
      this.results.push(testResult);
      return testResult;
    }
  }

  async runAllTests() {
    this.startTime = Date.now();
    this.results = [];
    
    console.log('🎯 Starting comprehensive debugging session...');
    console.log('📊 This will test: Auth, Database, MusicKit, Network, and more');

    // Test 1: Basic client health
    await this.runTest('Client Health', async () => {
      return {
        supabaseExists: !!supabase,
        url: supabase?.supabaseUrl,
        hasAuth: !!supabase?.auth,
        hasRealtime: !!supabase?.realtime,
        musicKitExists: !!(window as any).MusicKit,
        musicKitConfigured: !!(window as any).MusicKit?.getInstance
      };
    });

    // Test 2: Auth session
    await this.runTest('Auth Session', async () => {
      const { data, error } = await supabase.auth.getSession();
      return {
        hasSession: !!data.session,
        userId: data.session?.user?.id,
        email: data.session?.user?.email,
        expiresAt: data.session?.expires_at,
        error: error?.message
      };
    });

    // Test 3: Network connectivity
    await this.runTest('Network Test', async () => {
      const response = await fetch(`${supabase.supabaseUrl}/rest/v1/`, {
        method: 'HEAD',
        headers: {
          'apikey': supabase.supabaseKey,
          'Authorization': `Bearer ${supabase.supabaseKey}`
        }
      });
      
      return {
        status: response.status,
        ok: response.ok,
        headers: Object.fromEntries(response.headers.entries())
      };
    });

    // Test 4: Simple database query
    await this.runTest('DB Simple Query', async () => {
      const result = await supabase
        .from('subscription_plans')
        .select('id, name')
        .limit(1);
      
      return {
        success: !result.error,
        count: result.data?.length || 0,
        error: result.error?.message,
        status: result.status,
        data: result.data?.[0]
      };
    });

    // Test 5: Sessions table
    await this.runTest('Sessions Query', async () => {
      const result = await supabase
        .from('sessions')
        .select('id, name, active')
        .limit(1);
      
      return {
        success: !result.error,
        count: result.data?.length || 0,
        error: result.error?.message,
        status: result.status,
        data: result.data?.[0]
      };
    });

    // Test 6: User-specific sessions
    const { data: sessionData } = await supabase.auth.getSession();
    if (sessionData.session?.user?.id) {
      await this.runTest('User Sessions Query', async () => {
        const result = await supabase
          .from('sessions')
          .select('id, name, active')
          .eq('dj_id', sessionData.session!.user.id)
          .limit(1);
        
        return {
          success: !result.error,
          count: result.data?.length || 0,
          error: result.error?.message,
          status: result.status,
          data: result.data?.[0]
        };
      });
    }

    // Test 7: Other tables
    const tables = ['dj_profiles', 'dj_subscriptions', 'song_requests'];
    for (const table of tables) {
      await this.runTest(`${table} Query`, async () => {
        const result = await supabase
          .from(table)
          .select('*')
          .limit(1);
        
        return {
          success: !result.error,
          count: result.data?.length || 0,
          error: result.error?.message,
          status: result.status
        };
      });
    }

    // Test 8: MusicKit status
    await this.runTest('MusicKit Status', async () => {
      const musicKit = (window as any).MusicKit;
      if (!musicKit) {
        throw new Error('MusicKit not loaded');
      }

      return {
        loaded: true,
        configured: !!musicKit.getInstance,
        instance: !!musicKit.getInstance(),
        isAuthorized: musicKit.getInstance()?.isAuthorized || false,
        developerToken: !!musicKit.getInstance()?.developerToken
      };
    });

    // Test 9: Realtime connection
    await this.runTest('Realtime Test', async () => {
      return new Promise((resolve, reject) => {
        const channel = supabase
          .channel(`debug_test_${Date.now()}`)
          .subscribe((status) => {
            if (status === 'SUBSCRIBED') {
              channel.unsubscribe();
              resolve({ status, connected: true });
            } else if (status === 'CHANNEL_ERROR') {
              channel.unsubscribe();
              reject(new Error(`Realtime failed: ${status}`));
            }
          });
      });
    }, 10000);

    // Generate summary
    this.generateSummary();
  }

  private generateSummary() {
    const totalTime = Date.now() - this.startTime;
    const passed = this.results.filter(r => r.status === 'success').length;
    const failed = this.results.filter(r => r.status === 'error').length;
    const timeouts = this.results.filter(r => r.status === 'timeout').length;

    console.log('\n' + '='.repeat(60));
    console.log('📋 DEBUGGING SUMMARY REPORT');
    console.log('='.repeat(60));
    console.log(`⏱️  Total time: ${totalTime}ms`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⏰ Timeouts: ${timeouts}`);
    console.log(`📊 Total tests: ${this.results.length}`);

    console.log('\n🔍 DETAILED RESULTS:');
    this.results.forEach(result => {
      const icon = result.status === 'success' ? '✅' : result.status === 'timeout' ? '⏰' : '❌';
      console.log(`${icon} ${result.test}: ${result.duration}ms ${result.error || ''}`);
    });

    console.log('\n📋 COPY THIS JSON:');
    console.log(JSON.stringify({
      summary: { totalTime, passed, failed, timeouts, total: this.results.length },
      results: this.results,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent
    }, null, 2));

    // Store results globally
    (window as any).debugResults = this.results;
    (window as any).debugSummary = { totalTime, passed, failed, timeouts, total: this.results.length };

    console.log('\n🎯 Results stored in window.debugResults and window.debugSummary');
  }

  getResults() {
    return this.results;
  }
}

// Create global debugger instance
const simpleDebugger = new SimpleDebugger();
(window as any).runDebugTests = () => simpleDebugger.runAllTests();
(window as any).simpleDebugger = simpleDebugger;

console.log('🎯 Simple Debugger ready!');
console.log('📋 Run: window.runDebugTests() to start debugging');

export default simpleDebugger;
