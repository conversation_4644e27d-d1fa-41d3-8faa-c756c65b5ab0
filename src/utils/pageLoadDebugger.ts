// Auto-start debugger that runs immediately on page load
// This will capture what happens during the critical first moments

import { supabase } from '@/integrations/supabase/client';

interface DebugLog {
  timestamp: number;
  elapsed: number;
  event: string;
  data?: any;
  error?: string;
}

class PageLoadDebugger {
  private logs: DebugLog[] = [];
  private startTime: number;
  private isRunning: boolean = false;

  constructor() {
    this.startTime = Date.now();
    this.log('DEBUGGER_INIT', 'Page load debugger initialized');
  }

  private log(event: string, data?: any, error?: string) {
    const now = Date.now();
    const entry: DebugLog = {
      timestamp: now,
      elapsed: now - this.startTime,
      event,
      data,
      error
    };
    
    this.logs.push(entry);
    console.log(`[${entry.elapsed}ms] ${event}`, data || '', error || '');
  }

  async start() {
    if (this.isRunning) return;
    this.isRunning = true;
    
    this.log('DEBUG_START', 'Starting page load debugging');

    // Test 1: Immediate Supabase client check
    this.testSupabaseClient();

    // Test 2: Auth state monitoring
    this.monitorAuthState();

    // Test 3: Database connectivity tests
    this.testDatabaseConnectivity();

    // Test 4: Monitor for hanging queries
    this.monitorHangingQueries();

    // Test 5: Network monitoring
    this.monitorNetwork();

    // Auto-stop after 60 seconds
    setTimeout(() => this.stop(), 60000);
  }

  private testSupabaseClient() {
    try {
      this.log('CLIENT_CHECK', {
        exists: !!supabase,
        url: supabase?.supabaseUrl,
        hasAuth: !!supabase?.auth,
        hasRealtime: !!supabase?.realtime,
        realtimeConnected: supabase?.realtime?.isConnected()
      });
    } catch (error: any) {
      this.log('CLIENT_CHECK', null, error.message);
    }
  }

  private async monitorAuthState() {
    const checkAuth = async () => {
      try {
        const start = Date.now();
        const { data, error } = await supabase.auth.getSession();
        const duration = Date.now() - start;
        
        this.log('AUTH_CHECK', {
          duration,
          hasSession: !!data.session,
          userId: data.session?.user?.id,
          error: error?.message
        });
      } catch (error: any) {
        this.log('AUTH_CHECK', null, error.message);
      }
    };

    // Check immediately
    checkAuth();
    
    // Check every 2 seconds for first 30 seconds
    const interval = setInterval(() => {
      if (Date.now() - this.startTime > 30000) {
        clearInterval(interval);
        return;
      }
      checkAuth();
    }, 2000);
  }

  private async testDatabaseConnectivity() {
    const testTable = async (tableName: string) => {
      try {
        const start = Date.now();
        const result = await supabase
          .from(tableName)
          .select('*')
          .limit(1);
        const duration = Date.now() - start;
        
        this.log(`DB_${tableName.toUpperCase()}`, {
          duration,
          success: !result.error,
          count: result.data?.length || 0,
          error: result.error?.message,
          status: result.status
        });
      } catch (error: any) {
        this.log(`DB_${tableName.toUpperCase()}`, null, error.message);
      }
    };

    // Test different tables with delays
    setTimeout(() => testTable('subscription_plans'), 1000);
    setTimeout(() => testTable('dj_profiles'), 3000);
    setTimeout(() => testTable('sessions'), 5000);
    setTimeout(() => testTable('dj_subscriptions'), 7000);
  }

  private monitorHangingQueries() {
    // Override fetch to monitor all network requests
    const originalFetch = window.fetch;
    
    window.fetch = async (...args) => {
      const start = Date.now();
      const url = args[0]?.toString() || 'unknown';
      
      try {
        const response = await originalFetch(...args);
        const duration = Date.now() - start;
        
        if (url.includes('supabase.co')) {
          this.log('NETWORK_REQUEST', {
            url: url.split('?')[0], // Remove query params for privacy
            method: args[1]?.method || 'GET',
            status: response.status,
            duration,
            ok: response.ok
          });
        }
        
        return response;
      } catch (error: any) {
        const duration = Date.now() - start;
        this.log('NETWORK_REQUEST', {
          url: url.split('?')[0],
          duration,
          error: error.message
        });
        throw error;
      }
    };

    // Restore original fetch after 60 seconds
    setTimeout(() => {
      window.fetch = originalFetch;
      this.log('NETWORK_MONITOR', 'Fetch monitoring disabled');
    }, 60000);
  }

  private monitorNetwork() {
    // Test basic connectivity to Supabase
    const testConnectivity = async () => {
      try {
        const start = Date.now();
        const response = await fetch(`${supabase.supabaseUrl}/rest/v1/`, {
          method: 'HEAD',
          headers: {
            'apikey': supabase.supabaseKey
          }
        });
        const duration = Date.now() - start;
        
        this.log('CONNECTIVITY_TEST', {
          duration,
          status: response.status,
          ok: response.ok
        });
      } catch (error: any) {
        this.log('CONNECTIVITY_TEST', null, error.message);
      }
    };

    // Test immediately and every 10 seconds
    testConnectivity();
    const interval = setInterval(() => {
      if (Date.now() - this.startTime > 30000) {
        clearInterval(interval);
        return;
      }
      testConnectivity();
    }, 10000);
  }

  stop() {
    this.isRunning = false;
    this.log('DEBUG_STOP', 'Page load debugging stopped');
    
    // Make logs available globally
    (window as any).pageLoadDebugLogs = this.logs;
    
    console.log('🎯 Page Load Debug Complete! Access logs via window.pageLoadDebugLogs');
    console.log('📊 Summary:', {
      totalLogs: this.logs.length,
      duration: Date.now() - this.startTime,
      authChecks: this.logs.filter(l => l.event === 'AUTH_CHECK').length,
      dbQueries: this.logs.filter(l => l.event.startsWith('DB_')).length,
      networkRequests: this.logs.filter(l => l.event === 'NETWORK_REQUEST').length
    });
  }

  getLogs() {
    return this.logs;
  }

  getFormattedLogs() {
    return this.logs.map(log => 
      `[${log.elapsed}ms] ${log.event}: ${log.data ? JSON.stringify(log.data) : ''} ${log.error || ''}`
    ).join('\n');
  }
}

// Auto-start debugger
const pageDebugger = new PageLoadDebugger();

// Start debugging when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => pageDebugger.start());
} else {
  pageDebugger.start();
}

// Make debugger available globally
(window as any).pageLoadDebugger = pageDebugger;

export default pageDebugger;
